{"require": {"c10d/craft-recaptcha": "^2.3", "craftcms/ckeditor": "3.8.3", "craftcms/cms": "^4.0.0", "craftcms/contact-form": "3.1.0", "craftcms/element-api": "^4.1.0", "craftcms/feed-me": "^5.5.0", "doublesecretagency/craft-siteswitcher": "2.3.0", "dwy/facebook-conversion": "v1.3.3", "ether/seo": "^4.2.1", "hybridinteractive/craft-contact-form-extensions": "^4.2", "studioespresso/craft-dumper": "5.0.1", "topshelfcraft/wordsmith": "4.5.0", "verbb/image-resizer": "3.0.11", "verbb/super-table": "3.0.14", "vlucas/phpdotenv": "^5.4.0"}, "require-dev": {"craftcms/generator": "^1.7", "yiisoft/yii2-shell": "^2.0.3"}, "autoload": {"psr-4": {"modules\\": "modules/"}}, "config": {"allow-plugins": {"craftcms/plugin-installer": true, "yiisoft/yii2-composer": true}, "sort-packages": true, "optimize-autoloader": true, "platform": {"php": "8.0.2"}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}, "repositories": [{"type": "composer", "url": "https://composer.craftcms.com", "canonical": false}]}