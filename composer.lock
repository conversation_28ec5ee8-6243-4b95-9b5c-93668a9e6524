{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "3d22d6671e33a1e140a051d6115d5853", "packages": [{"name": "albert<PERSON>/invisible-recaptcha", "version": "v1.9.7", "dist": {"type": "zip", "url": "https://api.github.com/repos/albertcht/invisible-recaptcha/zipball/12a4aec36c32e4c4b97884ff6cae97859eb69098", "reference": "12a4aec36c32e4c4b97884ff6cae97859eb69098", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.2|^7.0", "illuminate/support": "^5.0|^6.0|^7.0|^8.0|^9.0", "illuminate/view": "^5.0|^6.0|^7.0|^8.0|^9.0", "php": "^5.6.4 || ^7.0 || ^8.0"}, "type": "library", "extra": {"laravel": {"providers": ["AlbertCht\\InvisibleReCaptcha\\InvisibleReCaptchaServiceProvider"]}}, "autoload": {"psr-4": {"AlbertCht\\InvisibleReCaptcha\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.albert-chen.com"}], "description": "Invisible reCAPTCHA For Laravel.", "keywords": ["<PERSON><PERSON>a", "invisible", "invisible-recap<PERSON>a", "laravel", "laravel5", "no-captcha", "php", "recaptcha"], "time": "2022-02-10T02:36:34+00:00"}, {"name": "c10d/craft-recaptcha", "version": "2.3.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/c10d-dev/craft-recaptcha/zipball/e1ba4437979b449423b249e9f7832ac3d92b170a", "reference": "e1ba4437979b449423b249e9f7832ac3d92b170a", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2"}, "type": "craft-plugin", "extra": {"name": "reCAPTCHA v3 for Craft CMS", "handle": "craft-recaptcha", "developer": "<PERSON><PERSON><PERSON>", "developerUrl": "https://c10d.dev", "documentationUrl": "https://github.com/c10d-dev/craft-recaptcha/blob/master/README.md", "changelogUrl": "https://raw.githubusercontent.com/c10d-dev/craft-recaptcha/master/CHANGELOG.md", "components": {"recaptcha": "c10d\\craftrecaptcha\\services\\RecaptchaService"}, "class": "c10d\\craftrecaptcha\\CraftRecaptcha"}, "autoload": {"psr-4": {"c10d\\craftrecaptcha\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://c10d.dev"}], "description": "Integrate reCAPTCHA v3 validation into your forms.", "keywords": ["cms", "craft", "craft-plugin", "craft-recaptcha", "craftcms", "recaptcha"], "support": {"docs": "https://github.com/c10d-dev/craft-recaptcha/blob/master/README.md", "issues": "https://github.com/c10d-dev/craft-recaptcha/issues"}, "time": "2024-03-22T00:58:59+00:00"}, {"name": "cakephp/core", "version": "3.9.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/core/zipball/4b45635d6be8a98be175fea9c9f575de29d515b3", "reference": "4b45635d6be8a98be175fea9c9f575de29d515b3", "shasum": ""}, "require": {"cakephp/utility": "^3.6.0", "php": ">=5.6.0"}, "suggest": {"cakephp/cache": "To use Configure::store() and restore().", "cakephp/event": "To use PluginApplicationInterface or plugin applications."}, "type": "library", "autoload": {"psr-4": {"Cake\\Core\\": "."}, "files": ["functions.php"]}, "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/core/graphs/contributors"}], "description": "CakePHP Framework Core classes", "homepage": "https://cakephp.org", "keywords": ["cakephp", "core", "framework"], "time": "2020-10-21T21:21:05+00:00"}, {"name": "cakephp/utility", "version": "3.9.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/utility/zipball/e655b399b7492e517caef52fb87af9db10543112", "reference": "e655b399b7492e517caef52fb87af9db10543112", "shasum": ""}, "require": {"cakephp/core": "^3.6.0", "php": ">=5.6.0"}, "suggest": {"ext-intl": "To use Text::transliterate() or Text::slug()", "lib-ICU": "To use Text::transliterate() or Text::slug()"}, "type": "library", "autoload": {"psr-4": {"Cake\\Utility\\": "."}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/utility/graphs/contributors"}], "description": "CakePHP Utility classes such as Inflector, String, Hash, and Security", "homepage": "https://cakephp.org", "keywords": ["cakephp", "hash", "inflector", "security", "string", "utility"], "time": "2020-08-18T13:55:20+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "2.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "time": "2023-12-11T17:09:12+00:00"}, {"name": "cebe/markdown", "version": "1.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "time": "2018-03-26T11:24:36+00:00"}, {"name": "commerceguys/addressing", "version": "v1.4.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/commerceguys/addressing/zipball/406c7b5f0fbe4f6a64155c0fe03b1adb34d01308", "reference": "406c7b5f0fbe4f6a64155c0fe03b1adb34d01308", "shasum": ""}, "require": {"doctrine/collections": "^1.2 || ^2.0", "php": ">=7.3"}, "suggest": {"symfony/validator": "to validate addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"CommerceGuys\\Addressing\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "description": "Addressing library powered by CLDR and Google's address data.", "keywords": ["address", "internationalization", "localization", "postal"], "time": "2023-02-15T10:11:14+00:00"}, {"name": "composer/ca-bundle", "version": "1.5.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/063d9aa8696582f5a41dffbbaf3c81024f0a604a", "reference": "063d9aa8696582f5a41dffbbaf3c81024f0a604a", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2024-07-08T15:28:20+00:00"}, {"name": "composer/class-map-generator", "version": "1.3.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/b1b3fd0b4eaf3ddf3ee230bc340bf3fff454a1a3", "reference": "b1b3fd0b4eaf3ddf3ee230bc340bf3fff454a1a3", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "time": "2024-06-12T14:13:04+00:00"}, {"name": "composer/composer", "version": "2.7.7", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/291942978f39435cf904d33739f98d7d4eca7b23", "reference": "291942978f39435cf904d33739f98d7d4eca7b23", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/class-map-generator": "^1.3.3", "composer/metadata-minifier": "^1.0", "composer/pcre": "^2.1 || ^3.1", "composer/semver": "^3.3", "composer/spdx-licenses": "^1.5.7", "composer/xdebug-handler": "^2.0.2 || ^3.0.3", "justinrainbow/json-schema": "^5.2.11", "php": "^7.2.5 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "react/promise": "^2.8 || ^3", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.2", "seld/signal-handler": "^2.0", "symfony/console": "^5.4.11 || ^6.0.11 || ^7", "symfony/filesystem": "^5.4 || ^6.0 || ^7", "symfony/finder": "^5.4 || ^6.0 || ^7", "symfony/polyfill-php73": "^1.24", "symfony/polyfill-php80": "^1.24", "symfony/polyfill-php81": "^1.24", "symfony/process": "^5.4 || ^6.0 || ^7"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.7-dev"}, "phpstan": {"includes": ["phpstan/rules.neon"]}}, "autoload": {"psr-4": {"Composer\\": "src/Composer/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "time": "2024-06-10T20:11:12+00:00"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "time": "2021-04-07T13:37:33+00:00"}, {"name": "composer/pcre", "version": "3.1.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/04229f163664973f68f38f6f73d917799168ef24", "reference": "04229f163664973f68f38f6f73d917799168ef24", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "time": "2024-05-27T13:40:54+00:00"}, {"name": "composer/semver", "version": "3.4.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/c51258e759afdb17f1fd1fe83bc12baaef6309d6", "reference": "c51258e759afdb17f1fd1fe83bc12baaef6309d6", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2024-07-12T11:35:52+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.8", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a", "reference": "560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "time": "2023-11-20T07:44:33+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "time": "2024-05-06T16:37:16+00:00"}, {"name": "craftcms/ckeditor", "version": "3.8.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/ckeditor/zipball/2dada986452c8a403c3ca652e87fe3a440fe9bf5", "reference": "2dada986452c8a403c3ca652e87fe3a440fe9bf5", "shasum": ""}, "require": {"craftcms/cms": "^4.5.0-beta.2", "craftcms/html-field": "^2.0.0-alpha.2", "nystudio107/craft-code-editor": ">=1.0.8 <=1.0.13 || ^1.0.16", "php": "^8.0.2"}, "type": "craft-plugin", "extra": {"name": "CKEditor", "handle": "ckeditor", "documentationUrl": "https://github.com/craftcms/ckeditor/blob/master/README.md"}, "autoload": {"psr-4": {"craft\\ckeditor\\": "src/"}}, "license": ["GPL-3.0-or-later"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Edit rich text content in Craft CMS using CKEditor.", "keywords": ["ckeditor", "cms", "craftcms", "html", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/ckeditor/issues?state=open", "source": "https://github.com/craftcms/ckeditor", "docs": "https://github.com/craftcms/ckeditor/blob/master/README.md", "rss": "https://github.com/craftcms/ckeditor/commits/master.atom"}, "time": "2024-03-28T19:52:16+00:00"}, {"name": "craftcms/cms", "version": "4.10.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/cms/zipball/95eb435befbdc8f2e785a27574c3ef76a4e5fdd0", "reference": "95eb435befbdc8f2e785a27574c3ef76a4e5fdd0", "shasum": ""}, "require": {"commerceguys/addressing": "^1.2", "composer/composer": "^2.7.0", "craftcms/plugin-installer": "~1.6.0", "craftcms/server-check": "~2.1.2", "creocoder/yii2-nested-sets": "~0.9.0", "elvanto/litemoji": "~4.3.0", "enshrined/svg-sanitize": "~0.16.0", "ext-bcmath": "*", "ext-curl": "*", "ext-dom": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-zip": "*", "guzzlehttp/guzzle": "^7.2.0", "illuminate/collections": "^9.1.0", "mikehaertl/php-shellcommand": "^1.6.3", "moneyphp/money": "^4.0", "monolog/monolog": "^2.3", "php": "^8.0.2", "pixelandtonic/imagine": "~*******", "samdark/yii2-psr-log-target": "^1.1.3", "seld/cli-prompt": "^1.0.4", "symfony/http-client": "^6.0.3", "symfony/var-dumper": "^5.0|^6.0", "symfony/yaml": "^5.2.3", "theiconic/name-parser": "^1.2", "twig/twig": "~3.8.0", "voku/stringy": "^6.4.0", "webonyx/graphql-php": "~14.11.5", "yiisoft/yii2": "~2.0.50", "yiisoft/yii2-debug": "~********", "yiisoft/yii2-queue": "~2.3.2", "yiisoft/yii2-symfonymailer": "^2.0.0"}, "conflict": {"webonyx/graphql-php": "14.11.7"}, "provide": {"bower-asset/inputmask": "5.0.9", "bower-asset/jquery": "3.6.1", "bower-asset/punycode": "2.3.1", "bower-asset/yii2-pjax": "2.0.8", "yii2tech/ar-softdelete": "1.0.4"}, "suggest": {"ext-exif": "Adds support for parsing image EXIF data.", "ext-iconv": "Adds support for more character encodings than PHP’s built-in mb_convert_encoding() function, which Craft will take advantage of when converting strings to UTF-8.", "ext-imagick": "Adds support for more image processing formats and options."}, "type": "library", "autoload": {"psr-4": {"craft\\": "src/", "yii2tech\\ar\\softdelete\\": "lib/ar-softdelete/src/"}}, "license": ["proprietary"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Craft CMS", "homepage": "https://craftcms.com", "keywords": ["cms", "craftcms", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/cms/issues?state=open", "forum": "https://craftcms.stackexchange.com/", "source": "https://github.com/craftcms/cms", "docs": "https://craftcms.com/docs/4.x/", "rss": "https://github.com/craftcms/cms/releases.atom"}, "time": "2024-07-16T23:21:49+00:00"}, {"name": "craftcms/contact-form", "version": "3.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/contact-form/zipball/1cfc5cfeb137e868d094ea15c90c6b9c4bf02ea5", "reference": "1cfc5cfeb137e868d094ea15c90c6b9c4bf02ea5", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0-beta.1|^5.0.0-beta.1", "php": "^8.0.2"}, "type": "craft-plugin", "extra": {"name": "Contact Form", "handle": "contact-form", "documentationUrl": "https://github.com/craftcms/contact-form/blob/v2/README.md", "components": {"mailer": "craft\\contactform\\Mailer"}}, "autoload": {"psr-4": {"craft\\contactform\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Add a simple contact form to your Craft CMS site", "keywords": ["cms", "contact", "craftcms", "form", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/contact-form/issues?state=open", "source": "https://github.com/craftcms/contact-form", "docs": "https://github.com/craftcms/contact-form/blob/v2/README.md", "rss": "https://github.com/craftcms/contact-form/commits/v2.atom"}, "time": "2024-03-11T22:40:47+00:00"}, {"name": "craftcms/element-api", "version": "4.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/element-api/zipball/3fbcff86e8caf912c4f614e24555deef7d846384", "reference": "3fbcff86e8caf912c4f614e24555deef7d846384", "shasum": ""}, "require": {"craftcms/cms": "^4.3.0|^5.0.0-beta.1", "league/fractal": "^0.20.1"}, "type": "craft-plugin", "extra": {"name": "Element API", "handle": "element-api", "documentationUrl": "https://github.com/craftcms/element-api/blob/v2/README.md"}, "autoload": {"psr-4": {"craft\\elementapi\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Create a JSON API for your elements in Craft", "keywords": ["api", "cms", "craftcms", "json", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/element-api/issues?state=open", "source": "https://github.com/craftcms/element-api", "docs": "https://github.com/craftcms/element-api/blob/v2/README.md", "rss": "https://github.com/craftcms/element-api/commits/v2.atom"}, "time": "2024-03-14T13:15:22+00:00"}, {"name": "craftcms/feed-me", "version": "5.6.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/feed-me/zipball/1194b6de7ca3a92fac4f8b1a308adf1bd5d272d1", "reference": "1194b6de7ca3a92fac4f8b1a308adf1bd5d272d1", "shasum": ""}, "require": {"cakephp/utility": "^3.3.12", "craftcms/cms": "^4.6.0", "jakeasmith/http_build_url": "^1.0", "league/csv": "^8.2 || ^9.0", "nesbot/carbon": "^1.22 || ^2.10", "php": "^8.0.2", "seld/jsonlint": "^1.7"}, "type": "craft-plugin", "extra": {"name": "Feed Me", "handle": "feed-me", "documentationUrl": "https://docs.craftcms.com/feed-me/v4/"}, "autoload": {"psr-4": {"craft\\feedme\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Import content from XML, RSS, CSV or JSON feeds into entries, categories, Craft Commerce products, and more.", "keywords": ["cms", "craft", "craftcms", "feed me"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/feed-me/issues?state=open", "source": "https://github.com/craftcms/feed-me", "docs": "https://docs.craftcms.com/feed-me/v4/", "rss": "https://github.com/craftcms/feed-me/commits/master.atom"}, "time": "2024-07-18T18:49:05+00:00"}, {"name": "craftcms/html-field", "version": "2.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/html-field/zipball/80c232ef92960748553afd27c87f2c0ef4bd4d93", "reference": "80c232ef92960748553afd27c87f2c0ef4bd4d93", "shasum": ""}, "require": {"craftcms/cms": "^4.2.0", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"craft\\htmlfield\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Base class for Craft CMS field types with HTML values.", "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/html-field/issues?state=open", "source": "https://github.com/craftcms/html-field", "docs": "https://github.com/craftcms/html-field/blob/main/README.md", "rss": "https://github.com/craftcms/html-field/commits/main.atom"}, "time": "2024-03-06T18:13:29+00:00"}, {"name": "craftcms/plugin-installer", "version": "1.6.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/plugin-installer/zipball/bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f", "reference": "bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4"}, "type": "composer-plugin", "extra": {"class": "craft\\composer\\Plugin"}, "autoload": {"psr-4": {"craft\\composer\\": "src/"}}, "license": ["MIT"], "description": "Craft CMS Plugin Installer", "homepage": "https://craftcms.com/", "keywords": ["cms", "composer", "craftcms", "installer", "plugin"], "time": "2023-02-22T13:17:00+00:00"}, {"name": "craftcms/server-check", "version": "2.1.8", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/server-check/zipball/c86e8aeabc73333111e2bfb4483d7b5402232e48", "reference": "c86e8aeabc73333111e2bfb4483d7b5402232e48", "shasum": ""}, "type": "library", "autoload": {"classmap": ["server/requirements"]}, "license": ["MIT"], "description": "Craft CMS Server Check", "homepage": "https://craftcms.com/", "keywords": ["cms", "craftcms", "requirements", "yii2"], "time": "2023-09-25T17:28:37+00:00"}, {"name": "creocoder/yii2-nested-sets", "version": "0.9.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/creocoder/yii2-nested-sets/zipball/cb8635a459b6246e5a144f096b992dcc30cf9954", "reference": "cb8635a459b6246e5a144f096b992dcc30cf9954", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"creocoder\\nestedsets\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The nested sets behavior for the Yii framework", "keywords": ["nested sets", "yii2"], "time": "2015-01-27T10:53:51+00:00"}, {"name": "davechild/textstatistics", "version": "1.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/DaveChild/Text-Statistics/zipball/e83d5f82726db80e662ae305fce3b3c41299b4f5", "reference": "e83d5f82726db80e662ae305fce3b3c41299b4f5", "shasum": ""}, "require": {"php": ">=7.2.0"}, "suggest": {"ext-bcmath": "More accurate floating point calculations.", "ext-mbstring": "Handle multi-byte text properly."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"DaveChild\\TextStatistics": "src"}}, "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.addedbytes.com/", "role": "Developer"}], "description": "PHP package to measure the readability of text according to various algorithms.", "homepage": "https://github.com/DaveChild/Text-Statistics", "time": "2018-08-21T08:17:10+00:00"}, {"name": "defuse/php-encryption", "version": "v2.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/f53396c2d34225064647a05ca76c1da9d99e5828", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "time": "2023-06-19T06:10:36+00:00"}, {"name": "doctrine/collections", "version": "1.8.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "time": "2022-09-01T20:12:10+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2024-02-05T11:35:39+00:00"}, {"name": "doublesecretagency/craft-siteswitcher", "version": "2.3.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/doublesecretagency/craft-siteswitcher/zipball/d2b92b9d252f34d6775f8477c2a2b9ab01b44cf0", "reference": "d2b92b9d252f34d6775f8477c2a2b9ab01b44cf0", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0-beta"}, "type": "craft-plugin", "extra": {"name": "Site Switcher", "handle": "site-switcher", "schemaVersion": "0.0.0", "changelogUrl": "https://raw.githubusercontent.com/doublesecretagency/craft-siteswitcher/v2/CHANGELOG.md", "class": "doublesecretagency\\siteswitcher\\SiteSwitcher"}, "autoload": {"psr-4": {"doublesecretagency\\siteswitcher\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Double Secret Agency", "homepage": "https://www.doublesecretagency.com/plugins"}], "description": "Easily switch between sites on any page of your website.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "language-switcher", "languages", "locales", "multi-site", "multisite", "sites", "switcher"], "support": {"docs": "https://github.com/doublesecretagency/craft-siteswitcher/blob/v2/README.md", "issues": "https://github.com/doublesecretagency/craft-siteswitcher/issues"}, "time": "2022-03-29T16:47:10+00:00"}, {"name": "dwy/facebook-conversion", "version": "v1.3.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/digitalwithyou/craft-facebook-conversion/zipball/4554af09be0770921946f57be097043ea9345123", "reference": "4554af09be0770921946f57be097043ea9345123", "shasum": ""}, "require": {"craftcms/cms": "^3.1|^4.0|^5.0", "facebook/php-business-sdk": "^19.0.2"}, "type": "craft-plugin", "extra": {"name": "Facebook Conversion", "handle": "facebook-conversion"}, "autoload": {"psr-4": {"dwy\\FacebookConversion\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "Digital With You", "homepage": "https://digitalwithyou.com"}, {"name": "<PERSON><PERSON>", "homepage": "https://diewy.be"}], "description": "Craft CMS plugin to send web events directly to Facebook", "keywords": ["craft", "craft-plugin", "craftcms", "facebook"], "support": {"email": "<EMAIL>", "issues": "https://github.com/digitalwithyou/craft-facebook-conversion/issues?state=open", "source": "https://github.com/digitalwithyou/craft-facebook-conversion", "docs": "https://digitalwithyou.com/en/plugins/facebook-conversion/getting-started"}, "time": "2024-04-01T13:10:28+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2023-06-01T07:04:22+00:00"}, {"name": "elvanto/litemoji", "version": "4.3.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/elvanto/litemoji/zipball/f13cf10686f7110a3b17d09de03050d0708840b8", "reference": "f13cf10686f7110a3b17d09de03050d0708840b8", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.3"}, "type": "library", "autoload": {"psr-4": {"LitEmoji\\": "src/"}}, "license": ["MIT"], "description": "A PHP library simplifying the conversion of unicode, HTML and shortcode emoji.", "keywords": ["emoji", "php-emoji"], "time": "2022-10-28T02:32:19+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.16.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/239e257605e2141265b429e40987b2ee51bba4b4", "reference": "239e257605e2141265b429e40987b2ee51bba4b4", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ezyang/htmlpurifier": "^4.16", "php": "^5.6 || ^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "time": "2023-03-20T10:51:12+00:00"}, {"name": "erusev/parsedown", "version": "1.7.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown/zipball/cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "time": "2019-12-30T22:54:17+00:00"}, {"name": "erusev/parsedown-extra", "version": "0.8.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown-extra/zipball/91ac3ff98f0cea243bdccc688df43810f044dcef", "reference": "91ac3ff98f0cea243bdccc688df43810f044dcef", "shasum": ""}, "require": {"erusev/parsedown": "^1.7.4"}, "type": "library", "autoload": {"psr-0": {"ParsedownExtra": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "An extension of Parsedown that adds support for Markdown Extra.", "homepage": "https://github.com/erusev/parsedown-extra", "keywords": ["markdown", "markdown extra", "parsedown", "parser"], "time": "2019-12-30T23:20:37+00:00"}, {"name": "ether/seo", "version": "4.2.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/ethercreative/seo/zipball/e501bd820efed88afdac1aedeac733778821e0d3", "reference": "e501bd820efed88afdac1aedeac733778821e0d3", "shasum": ""}, "require": {"craftcms/cms": "^4.0", "php": "^8.0"}, "type": "craft-plugin", "extra": {"handle": "seo", "name": "SEO", "developer": "<PERSON>ther <PERSON>", "developerUrl": "https://ethercreative.co.uk", "class": "ether\\seo\\Seo"}, "autoload": {"psr-4": {"ether\\seo\\": "src/"}}, "license": ["MIT"], "description": "SEO utilities including a unique field type, sitemap, & redirect manager", "support": {"issues": "https://github.com/ethercreative/seo", "docs": "https://github.com/ethercreative/seo/blob/v3/README.md"}, "time": "2024-02-16T10:16:16+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.17.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/bbc513d79acf6691fa9cf10f192c90dd2957f18c", "reference": "bbc513d79acf6691fa9cf10f192c90dd2957f18c", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2023-11-17T15:01:25+00:00"}, {"name": "facebook/php-business-sdk", "version": "19.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/facebook/facebook-php-business-sdk/zipball/f3eb099fa895ff6b5ddd98ba993982c2803b22d3", "reference": "f3eb099fa895ff6b5ddd98ba993982c2803b22d3", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.5 || ^7.0", "php": ">=8.0"}, "type": "library", "autoload": {"psr-4": {"FacebookAds\\": "src/FacebookAds/"}}, "description": "PHP SDK for Facebook Business", "homepage": "https://developers.facebook.com/", "keywords": ["ads", "business", "facebook", "instagram", "page", "sdk"], "time": "2024-04-15T18:22:25+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/fbd48bce38f73f8a4ec8583362e732e4095e5862", "reference": "fbd48bce38f73f8a4ec8583362e732e4095e5862", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.2"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "time": "2023-11-12T22:16:48+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "84ac2b2afc44e40d3e8e658a45d68d6d20437612"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/84ac2b2afc44e40d3e8e658a45d68d6d20437612", "reference": "84ac2b2afc44e40d3e8e658a45d68d6d20437612", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-18T11:52:56+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-07-18T10:29:17+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2024-07-18T11:15:46+00:00"}, {"name": "hybridinteractive/craft-contact-form-extensions", "version": "4.2.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/hybridinteractive/craft-contact-form-extensions/zipball/cb5a3ed50aa272b473aafd633db98f0ff4d8f5cc", "reference": "cb5a3ed50aa272b473aafd633db98f0ff4d8f5cc", "shasum": ""}, "require": {"albertcht/invisible-recaptcha": "^1.9.1", "composer/composer": "^2.2.12", "craftcms/cms": "^4.0.0", "craftcms/contact-form": "^3.0.0", "php": "^8.0.2"}, "type": "craft-plugin", "extra": {"name": "Contact Form Extensions", "handle": "contact-form-extensions", "changelogUrl": "https://github.com/hybridinteractive/craft-contact-form-extensions/blob/master/CHANGELOG.md", "documentationUrl": "https://github.com/hybridinteractive/craft-contact-form-extensions/blob/master/README.md", "components": {"contactFormExtensionsService": "hybridinteractive\\contactformextensions\\services\\ContactFormExtensionsService"}, "class": "hybridinteractive\\contactformextensions\\ContactFormExtensions"}, "autoload": {"psr-4": {"hybridinteractive\\contactformextensions\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Hybrid Interactive", "homepage": "https://hybridinteractive.io"}], "description": "Adds extensions to the Craft CMS contact form plugin.", "keywords": ["cms", "craft", "craft contact form extensions", "craft-plugin", "craftcms"], "support": {"docs": "https://github.com/hybridinteractive/craft-contact-form-extensions/blob/master/README.md", "issues": "https://github.com/hybridinteractive/craft-contact-form-extensions/issues"}, "time": "2022-11-14T20:07:45+00:00"}, {"name": "icanboogie/inflector", "version": "v2.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/ICanBoogie/Inflector/zipball/0288676d1cd48899386f8bbe50643d3ade5dc13b", "reference": "0288676d1cd48899386f8bbe50643d3ade5dc13b", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.1"}, "conflict": {"icanboogie/common": "<2.0"}, "type": "library", "autoload": {"files": ["lib/helpers.php"], "psr-4": {"ICanBoogie\\": "lib/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.weirdog.com/", "role": "Developer"}], "description": "Multilingual inflector that transforms words from singular to plural, underscore to camel case, and more.", "homepage": "http://icanboogie.org/", "keywords": ["camelize", "hyphenate", "inflect", "multilingual", "pluralize", "singularize", "underscore"], "time": "2023-03-12T18:39:52+00:00"}, {"name": "illuminate/bus", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/bus/zipball/4c719a19c3d8c34b2494a7206f8ffde3eff3f983", "reference": "4c719a19c3d8c34b2494a7206f8ffde3eff3f983", "shasum": ""}, "require": {"illuminate/collections": "^9.0", "illuminate/contracts": "^9.0", "illuminate/pipeline": "^9.0", "illuminate/support": "^9.0", "php": "^8.0.2"}, "suggest": {"illuminate/queue": "Required to use closures when chaining jobs (^7.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Bus package.", "homepage": "https://laravel.com", "time": "2023-04-18T13:42:14+00:00"}, {"name": "illuminate/collections", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/collections/zipball/d3710b0b244bfc62c288c1a87eaa62dd28352d1f", "reference": "d3710b0b244bfc62c288c1a87eaa62dd28352d1f", "shasum": ""}, "require": {"illuminate/conditionable": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "php": "^8.0.2"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^6.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "time": "2023-06-11T21:17:10+00:00"}, {"name": "illuminate/conditionable", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/conditionable/zipball/bea24daa0fa84b7e7b0d5b84f62c71b7e2dc3364", "reference": "bea24daa0fa84b7e7b0d5b84f62c71b7e2dc3364", "shasum": ""}, "require": {"php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Conditionable package.", "homepage": "https://laravel.com", "time": "2023-02-01T21:42:32+00:00"}, {"name": "illuminate/container", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/1641dda2d0750b68bb1264a3b37ff3973f2e6265", "reference": "1641dda2d0750b68bb1264a3b37ff3973f2e6265", "shasum": ""}, "require": {"illuminate/contracts": "^9.0", "php": "^8.0.2", "psr/container": "^1.1.1|^2.0.1"}, "provide": {"psr/container-implementation": "1.1|2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "time": "2023-01-24T16:54:18+00:00"}, {"name": "illuminate/contracts", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/44f65d723b13823baa02ff69751a5948bde60c22", "reference": "44f65d723b13823baa02ff69751a5948bde60c22", "shasum": ""}, "require": {"php": "^8.0.2", "psr/container": "^1.1.1|^2.0.1", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "time": "2023-02-08T14:36:30+00:00"}, {"name": "illuminate/events", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/events/zipball/8e534676bac23bc17925f5c74c128f9c09b98f69", "reference": "8e534676bac23bc17925f5c74c128f9c09b98f69", "shasum": ""}, "require": {"illuminate/bus": "^9.0", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "illuminate/support": "^9.0", "php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Events\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Events package.", "homepage": "https://laravel.com", "time": "2022-09-15T13:14:12+00:00"}, {"name": "illuminate/filesystem", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/filesystem/zipball/8168361548b2c5e2e501096cfbadb62a4a526290", "reference": "8168361548b2c5e2e501096cfbadb62a4a526290", "shasum": ""}, "require": {"illuminate/collections": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "illuminate/support": "^9.0", "php": "^8.0.2", "symfony/finder": "^6.0"}, "suggest": {"ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-hash": "Required to use the Filesystem class.", "illuminate/http": "Required for handling uploaded files (^7.0).", "league/flysystem": "Required to use the Flysystem local driver (^3.0.16).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.0).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.0).", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.0).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "symfony/filesystem": "Required to enable support for relative symbolic links (^6.0).", "symfony/mime": "Required to enable support for guessing extensions (^6.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Filesystem\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "time": "2023-02-10T20:24:35+00:00"}, {"name": "illuminate/macroable", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/macroable/zipball/e3bfaf6401742a9c6abca61b9b10e998e5b6449a", "reference": "e3bfaf6401742a9c6abca61b9b10e998e5b6449a", "shasum": ""}, "require": {"php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "time": "2022-08-09T13:29:29+00:00"}, {"name": "illuminate/pipeline", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pipeline/zipball/e0be3f3f79f8235ad7334919ca4094d5074e02f6", "reference": "e0be3f3f79f8235ad7334919ca4094d5074e02f6", "shasum": ""}, "require": {"illuminate/contracts": "^9.0", "illuminate/support": "^9.0", "php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pipeline\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pipeline package.", "homepage": "https://laravel.com", "time": "2022-06-09T14:13:53+00:00"}, {"name": "illuminate/support", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/223c608dbca27232df6213f776bfe7bdeec24874", "reference": "223c608dbca27232df6213f776bfe7bdeec24874", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "ext-ctype": "*", "ext-filter": "*", "ext-mbstring": "*", "illuminate/collections": "^9.0", "illuminate/conditionable": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "nesbot/carbon": "^2.62.1", "php": "^8.0.2", "voku/portable-ascii": "^2.0"}, "conflict": {"tightenco/collect": "<5.5.33"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (^9.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^2.0.2).", "ramsey/uuid": "Required to use Str::uuid() (^4.7).", "symfony/process": "Required to use the composer class (^6.0).", "symfony/uid": "Required to use Str::ulid() (^6.0).", "symfony/var-dumper": "Required to use the dd function (^6.0).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.4.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "time": "2023-06-11T21:11:53+00:00"}, {"name": "illuminate/view", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/view/zipball/0215165781b3269cdbecdfe63ffddd0e6cecfd6e", "reference": "0215165781b3269cdbecdfe63ffddd0e6cecfd6e", "shasum": ""}, "require": {"ext-tokenizer": "*", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/events": "^9.0", "illuminate/filesystem": "^9.0", "illuminate/macroable": "^9.0", "illuminate/support": "^9.0", "php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\View\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate View package.", "homepage": "https://laravel.com", "time": "2023-03-13T01:21:46+00:00"}, {"name": "jakeasmith/http_build_url", "version": "1.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/jakeasmith/http_build_url/zipball/93c273e77cb1edead0cf8bcf8cd2003428e74e37", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37", "shasum": ""}, "type": "library", "autoload": {"files": ["src/http_build_url.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality for http_build_url() to environments without pecl_http.", "time": "2017-05-01T15:36:40+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.3.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/feb2ca6dd1cebdaf1ed60a4c8de2e53ce11c4fd8", "reference": "feb2ca6dd1cebdaf1ed60a4c8de2e53ce11c4fd8", "shasum": ""}, "require": {"php": ">=7.1"}, "bin": ["bin/validate-json"], "type": "library", "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2024-07-06T21:00:26+00:00"}, {"name": "league/csv", "version": "9.8.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/9d2e0265c5d90f5dd601bc65ff717e05cec19b47", "reference": "9d2e0265c5d90f5dd601bc65ff717e05cec19b47", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.4 || ^8.0"}, "suggest": {"ext-dom": "Required to use the XMLConverter and or the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"League\\Csv\\": "src"}, "files": ["src/functions_include.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "CSV data manipulation made easy in PHP", "homepage": "https://csv.thephpleague.com", "keywords": ["convert", "csv", "export", "filter", "import", "read", "transform", "write"], "time": "2022-01-04T00:13:07+00:00"}, {"name": "league/fractal", "version": "0.20.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/fractal/zipball/8b9d39b67624db9195c06f9c1ffd0355151eaf62", "reference": "8b9d39b67624db9195c06f9c1ffd0355151eaf62", "shasum": ""}, "require": {"php": ">=7.4"}, "suggest": {"illuminate/pagination": "The Illuminate Pagination component.", "pagerfanta/pagerfanta": "Pagerfant<PERSON>", "zendframework/zend-paginator": "Zend Framework Paginator"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.20.x-dev"}}, "autoload": {"psr-4": {"League\\Fractal\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://philsturgeon.uk/", "role": "Developer"}], "description": "Handle the output of complex data structures ready for API output.", "homepage": "http://fractal.thephpleague.com/", "keywords": ["api", "json", "league", "rest"], "time": "2022-04-11T12:47:17+00:00"}, {"name": "lsolesen/pel", "version": "0.9.12", "dist": {"type": "zip", "url": "https://api.github.com/repos/pel/pel/zipball/b95fe29cdacf9d36330da277f10910a13648c84c", "reference": "b95fe29cdacf9d36330da277f10910a13648c84c", "shasum": ""}, "require": {"php": ">=7.1.0"}, "type": "library", "autoload": {"psr-4": {"lsolesen\\pel\\": "src/"}}, "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://intraface.dk", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://geisler.net", "role": "Developer"}], "description": "PHP Exif Library. A library for reading and writing Exif headers in JPEG and TIFF images using PHP.", "homepage": "http://pel.github.com/pel/", "keywords": ["exif", "image"], "time": "2022-02-18T13:20:54+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "time": "2024-03-31T07:05:07+00:00"}, {"name": "mikehaertl/php-shellcommand", "version": "1.7.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-shellcommand/zipball/e79ea528be155ffdec6f3bf1a4a46307bb49e545", "reference": "e79ea528be155ffdec6f3bf1a4a46307bb49e545", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "type": "library", "autoload": {"psr-4": {"mikehaertl\\shellcommand\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An object oriented interface to shell commands", "keywords": ["shell"], "time": "2023-04-19T08:25:22+00:00"}, {"name": "mofodojodino/profanity-filter", "version": "1.3.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/mofodojodino/ProfanityFilter/zipball/529d24adcdb5b9507a4622db6aba6e54c5bb82a9", "reference": "529d24adcdb5b9507a4622db6aba6e54c5bb82a9", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"psr-0": {"": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filters out profanities from strings.", "time": "2016-05-11T00:19:07+00:00"}, {"name": "moneyphp/money", "version": "v4.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/5e60aebf09f709dd4ea16bf85e66d65301c0d172", "reference": "5e60aebf09f709dd4ea16bf85e66d65301c0d172", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-filter": "*", "ext-json": "*", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "suggest": {"ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "time": "2024-01-24T08:29:16+00:00"}, {"name": "monolog/monolog", "version": "2.9.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/a30bfe2e142720dfa990d0a7e573997f5d884215", "reference": "a30bfe2e142720dfa990d0a7e573997f5d884215", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2024-04-12T20:52:51+00:00"}, {"name": "mundschenk-at/php-typography", "version": "v6.7.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/mundschenk-at/php-typography/zipball/6b0414811f42e53e101f5ea5a8023c63eafd3dcb", "reference": "6b0414811f42e53e101f5ea5a8023c63eafd3dcb", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-pcre": "*", "masterminds/html5": "^2.5.0", "php": ">=7.4.0"}, "bin": ["src/bin/update-patterns.php", "src/bin/update-iana.php"], "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://code.mundschenk.at", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kingdesk.com", "role": "Original author"}], "description": "A PHP library for improving your web typography", "time": "2022-11-14T22:30:08+00:00"}, {"name": "nesbot/carbon", "version": "2.72.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/afd46589c216118ecd48ff2b95d77596af1e57ed", "reference": "afd46589c216118ecd48ff2b95d77596af1e57ed", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev", "dev-2.x": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2024-06-03T19:18:41+00:00"}, {"name": "nystudio107/craft-code-editor", "version": "1.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-code-editor/zipball/a793406e62cd6c7d8e25ac5e0fb2208b4206815b", "reference": "a793406e62cd6c7d8e25ac5e0fb2208b4206815b", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0 || ^4.0.0 || ^5.0.0", "phpdocumentor/reflection-docblock": "^5.0.0"}, "type": "yii2-extension", "extra": {"bootstrap": "nystudio107\\codeeditor\\CodeEditor"}, "autoload": {"psr-4": {"nystudio107\\codeeditor\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Provides a code editor field with Twig & Craft API autocomplete", "keywords": ["Craft", "Monaco", "cms", "code", "craftcms", "css", "editor", "javascript", "markdown", "twig"], "time": "2024-04-15T16:35:48+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2020-10-15T08:29:30+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.4.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c", "reference": "9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7", "webmozart/assert": "^1.9.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2024-05-21T05:55:05+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.8.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/153ae662783729388a584b4361f2545e4d841e3c", "reference": "153ae662783729388a584b4361f2545e4d841e3c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2024-02-23T11:10:43+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/80735db690fe4fc5c76dfa7f9b770634285fa820", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "time": "2023-11-12T21:59:55+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.29.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/fcaefacf2d5c417e928405b71b400d4ce10daaf4", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "time": "2024-05-31T08:52:43+00:00"}, {"name": "pixelandtonic/imagine", "version": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/pixelandtonic/Imagine/zipball/4d9bb596ff60504e37ccf9103c0bb705dba7fec6", "reference": "4d9bb596ff60504e37ccf9103c0bb705dba7fec6", "shasum": ""}, "require": {"php": ">=5.5"}, "suggest": {"ext-exif": "to read EXIF metadata", "ext-gd": "to use the GD implementation", "ext-gmagick": "to use the Gmagick implementation", "ext-imagick": "to use the Imagick implementation"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "1.x-dev"}}, "autoload": {"psr-4": {"Imagine\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://avalanche123.com"}], "description": "Image processing for PHP 5.3", "homepage": "http://imagine.readthedocs.org/", "keywords": ["drawing", "graphics", "image manipulation", "image processing"], "time": "2023-01-03T19:18:06+00:00"}, {"name": "psr/clock", "version": "1.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2021-07-14T16:46:02+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2021-10-29T13:26:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v3.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/8a164643313c71354582dc850b42b33fa12a4b63", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "time": "2024-05-24T10:39:05+00:00"}, {"name": "samdark/yii2-psr-log-target", "version": "1.1.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/samdark/yii2-psr-log-target/zipball/5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea", "reference": "5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea", "shasum": ""}, "require": {"psr/log": "~1.0.2|~1.1.0|~3.0.0", "yiisoft/yii2": "~2.0.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"samdark\\log\\": "src", "samdark\\log\\tests\\": "tests"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii 2 log target which uses PSR-3 compatible logger", "homepage": "https://github.com/samdark/yii2-psr-log-target", "keywords": ["extension", "log", "psr-3", "yii"], "time": "2023-11-23T14:11:29+00:00"}, {"name": "seld/cli-prompt", "version": "1.0.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/cli-prompt/zipball/b8dfcf02094b8c03b40322c229493bb2884423c5", "reference": "b8dfcf02094b8c03b40322c229493bb2884423c5", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\CliPrompt\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Allows you to prompt for user input on the command line, and optionally hide the characters they type", "keywords": ["cli", "console", "hidden", "input", "prompt"], "time": "2020-12-15T21:32:01+00:00"}, {"name": "seld/jsonlint", "version": "1.11.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/1748aaf847fc731cfad7725aec413ee46f0cc3a2", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2024-07-11T14:55:45+00:00"}, {"name": "seld/phar-utils", "version": "1.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "time": "2022-08-31T10:31:18+00:00"}, {"name": "seld/signal-handler", "version": "2.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/signal-handler/zipball/04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Seld\\Signal\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Simple unix signal handler that silently fails where signals are not supported for easy cross-platform development", "keywords": ["posix", "sigint", "signal", "sigterm", "unix"], "time": "2023-09-03T09:24:00+00:00"}, {"name": "studioespresso/craft-dumper", "version": "5.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/studioespresso/craft-dumper/zipball/3f5c1cfc44cffcefebe9c9b83e94c40b5b75dbc4", "reference": "3f5c1cfc44cffcefebe9c9b83e94c40b5b75dbc4", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0|^5.0.0-alpha"}, "type": "craft-plugin", "extra": {"name": "<PERSON><PERSON>", "handle": "dumper", "schemaVersion": "1.0.0", "hasCpSettings": false, "hasCpSection": false, "changelogUrl": "https://raw.githubusercontent.com/studioespresso/craft-dumper/master/CHANGELOG.md", "class": "studioespresso\\craftdumper\\CraftDumper"}, "autoload": {"psr-4": {"studioespresso\\craftdumper\\": "src/"}, "files": ["src/helper.php"]}, "license": ["MIT"], "authors": [{"name": "Studio Espresso", "homepage": "https://www.studioespresso.co"}], "description": "Bringing symfony/VarDumper to Craft CMS", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "dumper"], "support": {"docs": "https://github.com/studioespresso/craft-dumper/blob/master/README.md", "issues": "https://github.com/studioespresso/craft-dumper/issues"}, "time": "2024-02-21T14:48:06+00:00"}, {"name": "symfony/console", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/c3ebc83d031b71c39da318ca8b7a07ecc67507ed", "reference": "c3ebc83d031b71c39da318ca8b7a07ecc67507ed", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.4|^6.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "time": "2022-01-02T09:55:41+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2eaf8e63bc5b8cefabd4a800157f0d0c094f677a", "reference": "2eaf8e63bc5b8cefabd4a800157f0d0c094f677a", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/event-dispatcher-contracts": "^2|^3"}, "conflict": {"symfony/dependency-injection": "<5.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7bc61cc2db649b4637d331240c5346dcc7708051", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2022-01-02T09:55:41+00:00"}, {"name": "symfony/filesystem", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/3d49eec03fda1f0fc19b7349fbbe55ebc1004214", "reference": "3d49eec03fda1f0fc19b7349fbbe55ebc1004214", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "time": "2023-01-20T17:44:14+00:00"}, {"name": "symfony/finder", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/5cc9cac6586fc0c28cd173780ca696e419fefa11", "reference": "5cc9cac6586fc0c28cd173780ca696e419fefa11", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "time": "2023-01-20T17:44:14+00:00"}, {"name": "symfony/http-client", "version": "v6.0.20", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/541c04560da1875f62c963c3aab6ea12a7314e11", "reference": "541c04560da1875f62c963c3aab6ea12a7314e11", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "time": "2023-01-30T15:41:07+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/4184b9b63af1edaf35b6a7974c6f1f9f33294129", "reference": "4184b9b63af1edaf35b6a7974c6f1f9f33294129", "shasum": ""}, "require": {"php": ">=8.0.2"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2022-04-12T16:11:42+00:00"}, {"name": "symfony/mailer", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/cd60799210c488f545ddde2444dc1aa548322872", "reference": "cd60799210c488f545ddde2444dc1aa548322872", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.0.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/http-kernel": "<5.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "time": "2023-01-11T11:50:03+00:00"}, {"name": "symfony/mime", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/d7052547a0070cbeadd474e172b527a00d657301", "reference": "d7052547a0070cbeadd474e172b527a00d657301", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<5.4.14|>=6.0,<6.0.14|>=6.1,<6.1.6"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "time": "2023-01-11T11:50:03+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "reference": "0424dff1c58f028c451efff2045f5d92410bd540", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/c027e6a3c6aee334663ec21f5852e89738abc805", "reference": "c027e6a3c6aee334663ec21f5852e89738abc805", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/64647a7c30b2283f5d49b874d84a18fc22054b7a", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/a95281b0be0d9ab48050ebd988b967875cdb9fdb", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fd22ab50000ef01661e2a31d850ebaa297f8e03c", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/10112722600777e02d2745716b70c5db4ca70442", "reference": "10112722600777e02d2745716b70c5db4ca70442", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/ec444d3f3f6505bb28d11afa41e75faadebc10a1", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.30.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/3fb075789fb91f9ad9af537c4012d523085bd5af", "reference": "3fb075789fb91f9ad9af537c4012d523085bd5af", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/process", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/2114fd60f26a296cc403a7939ab91478475a33d4", "reference": "2114fd60f26a296cc403a7939ab91478475a33d4", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/service-contracts", "version": "v3.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "reference": "d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/container": "^2.0"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2022-05-30T19:17:58+00:00"}, {"name": "symfony/string", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/d9e72497367c23e08bf94176d2be45b00a9d232a", "reference": "d9e72497367c23e08bf94176d2be45b00a9d232a", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/translation", "version": "v6.0.19", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f", "reference": "9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.3|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/acbfbb274e730e5a0236f619b6168d9dedb3e282", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282", "shasum": ""}, "require": {"php": ">=8.0.2"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2022-06-27T17:10:44+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.40", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/af8868a6e9d6082dfca11f1a1f205ae93a8b6d93", "reference": "af8868a6e9d6082dfca11f1a1f205ae93a8b6d93", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/yaml", "version": "v5.4.40", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/81cad0ceab3d61fe14fe941ff18a230ac9c80f83", "reference": "81cad0ceab3d61fe14fe941ff18a230ac9c80f83", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "time": "2024-05-31T14:33:22+00:00"}, {"name": "theiconic/name-parser", "version": "v1.2.11", "dist": {"type": "zip", "url": "https://api.github.com/repos/theiconic/name-parser/zipball/9a54a713bf5b2e7fd990828147d42de16bf8a253", "reference": "9a54a713bf5b2e7fd990828147d42de16bf8a253", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "autoload": {"psr-4": {"TheIconic\\NameParser\\": ["src/", "tests/"]}}, "license": ["MIT"], "authors": [{"name": "The Iconic", "email": "<EMAIL>"}], "description": "PHP library for parsing a string containing a full name into its parts", "time": "2019-11-14T14:08:48+00:00"}, {"name": "topshelfcraft/base", "version": "4.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/TopShelfCraft/base/zipball/5e0376d39a2b84a0cc1b661c2ed2dd3742077f87", "reference": "5e0376d39a2b84a0cc1b661c2ed2dd3742077f87", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"TopShelfCraft\\base\\": "src/"}}, "description": "Base metal for Top Shelf Craft modules and plugins.", "time": "2022-12-20T02:05:36+00:00"}, {"name": "topshelfcraft/wordsmith", "version": "4.5.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/TopShelfCraft/Wordsmith/zipball/e24f31177e7265c1c05e268825a77baf3f7b6bea", "reference": "e24f31177e7265c1c05e268825a77baf3f7b6bea", "shasum": ""}, "require": {"craftcms/cms": "^4.5", "davechild/textstatistics": "^1.0.3", "erusev/parsedown-extra": "^0.8", "icanboogie/inflector": "^2.0", "mofodojodino/profanity-filter": "^1.3", "mundschenk-at/php-typography": "^6.0", "topshelfcraft/base": "^4.0.1", "voku/stringy": "^6.4.0"}, "type": "craft-plugin", "extra": {"name": "<PERSON><PERSON>", "handle": "wordsmith", "class": "TopShelfCraft\\Wordsmith\\Wordsmith"}, "autoload": {"psr-4": {"TopShelfCraft\\Wordsmith\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "Top Shelf <PERSON> (<PERSON>)", "homepage": "https://topshelfcraft.com"}], "description": "...because you have the best words.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "wordsmith"], "support": {"docs": "https://wordsmith.docs.topshelfcraft.com/", "issues": "https://github.com/TopShelfCraft/Wordsmith/issues"}, "time": "2024-06-24T01:35:32+00:00"}, {"name": "twig/twig", "version": "v3.8.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/9d15f0ac07f44dc4217883ec6ae02fd555c6f71d", "reference": "9d15f0ac07f44dc4217883ec6ae02fd555c6f71d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php80": "^1.22"}, "type": "library", "autoload": {"psr-4": {"Twig\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2023-11-21T18:54:41+00:00"}, {"name": "verbb/base", "version": "2.0.8", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/verbb-base/zipball/1445a89d7a360d3ba8bba97c47a10c6f1aa27307", "reference": "1445a89d7a360d3ba8bba97c47a10c6f1aa27307", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2"}, "type": "yii-module", "autoload": {"psr-4": {"verbb\\base\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Common utilities and building-blocks for Verbb plugins for Craft CMS.", "time": "2024-06-10T09:00:32+00:00"}, {"name": "verbb/image-resizer", "version": "3.0.11", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/image-resizer/zipball/f1d4a22afdbc22af65aa851230b4eaae72694bc9", "reference": "f1d4a22afdbc22af65aa851230b4eaae72694bc9", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "lsolesen/pel": "^0.9.6", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "type": "craft-plugin", "extra": {"name": "Image Resizer", "handle": "image-resizer", "description": "Image Resizer resizes your assets when they are uploaded.", "documentationUrl": "https://github.com/verbb/image-resizer", "changelogUrl": "https://raw.githubusercontent.com/verbb/image-resizer/craft-4/CHANGELOG.md", "class": "verbb\\imageresizer\\ImageResizer"}, "autoload": {"psr-4": {"verbb\\imageresizer\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Resize assets when they are uploaded.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "example"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/image-resizer/issues?state=open", "source": "https://github.com/verbb/image-resizer", "docs": "https://github.com/verbb/image-resizer", "rss": "https://github.com/verbb/image-resizer/commits/v2.atom"}, "time": "2024-01-30T12:35:34+00:00"}, {"name": "verbb/super-table", "version": "3.0.14", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/super-table/zipball/31e8ef50bc0dc0e8e08761a02c78928c672ab5ce", "reference": "31e8ef50bc0dc0e8e08761a02c78928c672ab5ce", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "type": "craft-plugin", "extra": {"name": "Super Table", "handle": "super-table", "description": "Super-charge your Craft workflow with Super Table. Use it to group fields together or build complex Matrix-in-Matrix solutions.", "documentationUrl": "https://github.com/verbb/super-table", "changelogUrl": "https://raw.githubusercontent.com/verbb/super-table/craft-4/CHANGELOG.md", "class": "verbb\\supertable\\SuperTable"}, "autoload": {"psr-4": {"verbb\\supertable\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Super-charge your content builders and create nested Matrix fields.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "super table"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/super-table/issues?state=open", "source": "https://github.com/verbb/super-table", "docs": "https://github.com/verbb/super-table", "rss": "https://github.com/verbb/super-table/commits/v2.atom"}, "time": "2024-04-29T01:19:08+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4", "reference": "2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.2", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.2", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2023-11-12T22:43:29+00:00"}, {"name": "voku/anti-xss", "version": "4.1.42", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/anti-xss/zipball/bca1f8607e55a3c5077483615cd93bd8f11bd675", "reference": "bca1f8607e55a3c5077483615cd93bd8f11bd675", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-utf8": "~6.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["MIT"], "authors": [{"name": "EllisLab Dev Team", "homepage": "http://ellislab.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/"}], "description": "anti xss-library", "homepage": "https://github.com/voku/anti-xss", "keywords": ["anti-xss", "clean", "security", "xss"], "time": "2023-07-03T14:40:46+00:00"}, {"name": "voku/arrayy", "version": "7.9.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Arrayy/zipball/0e20b8c6eef7fc46694a2906e0eae2f9fc11cade", "reference": "0e20b8c6eef7fc46694a2906e0eae2f9fc11cade", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.0.0", "phpdocumentor/reflection-docblock": "~4.3 || ~5.0", "symfony/polyfill-mbstring": "~1.0"}, "type": "library", "autoload": {"files": ["src/Create.php"], "psr-4": {"Arrayy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/", "role": "Maintainer"}], "description": "Array manipulation library for PHP, called Arrayy!", "keywords": ["A<PERSON>yy", "array", "helpers", "manipulation", "methods", "utility", "utils"], "time": "2022-12-27T12:58:32+00:00"}, {"name": "voku/email-check", "version": "3.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/email-check/zipball/6ea842920bbef6758b8c1e619fd1710e7a1a2cac", "reference": "6ea842920bbef6758b8c1e619fd1710e7a1a2cac", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-intl-idn": "~1.10"}, "suggest": {"ext-intl": "Use Intl for best performance"}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "email-check (syntax, dns, trash, ...) library", "homepage": "https://github.com/voku/email-check", "keywords": ["check-email", "email", "mail", "mail-check", "validate-email", "validate-email-address", "validate-mail"], "time": "2021-01-27T14:14:33+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b56450eed252f6801410d810c8e1727224ae0743", "reference": "b56450eed252f6801410d810c8e1727224ae0743", "shasum": ""}, "require": {"php": ">=7.0.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "time": "2022-03-08T17:03:00+00:00"}, {"name": "voku/portable-utf8", "version": "6.0.13", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-utf8/zipball/b8ce36bf26593e5c2e81b1850ef0ffb299d2043f", "reference": "b8ce36bf26593e5c2e81b1850ef0ffb299d2043f", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-iconv": "~1.0", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.0", "voku/portable-ascii": "~2.0.0"}, "suggest": {"ext-ctype": "Use Ctype for e.g. hexadecimal digit detection", "ext-fileinfo": "Use Fileinfo for better binary file detection", "ext-iconv": "Use iconv for best performance", "ext-intl": "Use Intl for best performance", "ext-json": "Use JSON for string detection", "ext-mbstring": "Use Mbstring for best performance"}, "type": "library", "autoload": {"files": ["bootstrap.php"], "psr-4": {"voku\\": "src/voku/"}}, "license": ["(Apache-2.0 or GPL-2.0)"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "http://pageconfig.com/"}, {"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable UTF-8 library - performance optimized (unicode) string functions for php.", "homepage": "https://github.com/voku/portable-utf8", "keywords": ["UTF", "clean", "php", "unicode", "utf-8", "utf8"], "time": "2023-03-08T08:35:38+00:00"}, {"name": "voku/stop-words", "version": "2.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/stop-words/zipball/8e63c0af20f800b1600783764e0ce19e53969f71", "reference": "8e63c0af20f800b1600783764e0ce19e53969f71", "shasum": ""}, "require": {"php": ">=7.0.0"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Stop-Words via PHP", "keywords": ["stop words", "stop-words"], "time": "2018-11-23T01:37:27+00:00"}, {"name": "voku/stringy", "version": "6.5.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Stringy/zipball/c453c88fbff298f042c836ef44306f8703b2d537", "reference": "c453c88fbff298f042c836ef44306f8703b2d537", "shasum": ""}, "require": {"defuse/php-encryption": "~2.0", "ext-json": "*", "php": ">=7.0.0", "voku/anti-xss": "~4.1", "voku/arrayy": "~7.8", "voku/email-check": "~3.1", "voku/portable-ascii": "~2.0", "voku/portable-utf8": "~6.0", "voku/urlify": "~5.0"}, "replace": {"danielstjules/stringy": "~3.0"}, "type": "library", "autoload": {"files": ["src/Create.php"], "psr-4": {"Stringy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/", "role": "Fork-Maintainer"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2022-03-28T14:52:20+00:00"}, {"name": "voku/urlify", "version": "5.0.7", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/urlify/zipball/014b2074407b5db5968f836c27d8731934b330e4", "reference": "014b2074407b5db5968f836c27d8731934b330e4", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-ascii": "~2.0", "voku/portable-utf8": "~6.0", "voku/stop-words": "~2.0"}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.johnnybroadway.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://moelleken.org/"}], "description": "PHP port of URLify.js from the Django project. Transliterates non-ascii characters for use in URLs.", "homepage": "https://github.com/voku/urlify", "keywords": ["encode", "iconv", "link", "slug", "translit", "transliterate", "transliteration", "url", "urlify"], "time": "2022-01-24T19:08:46+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2022-06-03T18:03:27+00:00"}, {"name": "webonyx/graphql-php", "version": "v14.11.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/d9c2fdebc6aa01d831bc2969da00e8588cffef19", "reference": "d9c2fdebc6aa01d831bc2969da00e8588cffef19", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.1 || ^8"}, "suggest": {"psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "type": "library", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "time": "2023-07-05T14:23:37+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.51", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "ea1f112f4dc9a9824e77b788019e2d53325d823c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/ea1f112f4dc9a9824e77b788019e2d53325d823c", "reference": "ea1f112f4dc9a9824e77b788019e2d53325d823c", "shasum": ""}, "require": {"bower-asset/inputmask": "^5.0.8 ", "bower-asset/jquery": "3.7.*@stable | 3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "^2.2", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "^4.17", "lib-pcre": "*", "paragonie/random_compat": ">=1", "php": ">=7.3.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "support": {"forum": "https://forum.yiiframework.com/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2/issues?state=open", "source": "https://github.com/yiisoft/yii2", "wiki": "https://www.yiiframework.com/wiki"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "time": "2024-07-18T19:50:00+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/94bb3f66e779e2774f8776d6e1bdeab402940510", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "time": "2020-06-24T00:04:01+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.1.22", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/c0fa388c56b64edfb92987fdcc37d7a0243170d7", "reference": "c0fa388c56b64edfb92987fdcc37d7a0243170d7", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}}}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "yii2"], "time": "2022-11-18T17:29:27+00:00"}, {"name": "yiisoft/yii2-queue", "version": "2.3.7", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-queue/zipball/dbc9d4a7b2a6995cd19c3e334227482ef55e559b", "reference": "dbc9d4a7b2a6995cd19c3e334227482ef55e559b", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/process": "^3.3||^4.0||^5.0||^6.0||^7.0", "yiisoft/yii2": "~2.0.14"}, "suggest": {"aws/aws-sdk-php": "Need for aws SQS.", "enqueue/amqp-lib": "Need for AMQP interop queue.", "enqueue/stomp": "Need for Stomp queue.", "ext-gearman": "Need for Gearman queue.", "ext-pcntl": "Need for process signals.", "pda/pheanstalk": "Need for Beanstalk queue.", "php-amqplib/php-amqplib": "Need for AMQP queue.", "yiisoft/yii2-redis": "Need for Redis queue."}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\queue\\": "src", "yii\\queue\\db\\": "src/drivers/db", "yii\\queue\\sqs\\": "src/drivers/sqs", "yii\\queue\\amqp\\": "src/drivers/amqp", "yii\\queue\\file\\": "src/drivers/file", "yii\\queue\\sync\\": "src/drivers/sync", "yii\\queue\\redis\\": "src/drivers/redis", "yii\\queue\\stomp\\": "src/drivers/stomp", "yii\\queue\\gearman\\": "src/drivers/gearman", "yii\\queue\\beanstalk\\": "src/drivers/beanstalk", "yii\\queue\\amqp_interop\\": "src/drivers/amqp_interop"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "z<PERSON>av<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Yii2 Queue Extension which supported DB, Redis, RabbitMQ, Beanstalk, SQS and Gearman", "keywords": ["async", "beanstalk", "db", "gearman", "gii", "queue", "rabbitmq", "redis", "sqs", "yii"], "time": "2024-04-29T09:40:52+00:00"}, {"name": "yiisoft/yii2-symfonymailer", "version": "2.0.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-symfonymailer/zipball/82f5902551a160633c4734b5096977ce76a809d9", "reference": "82f5902551a160633c4734b5096977ce76a809d9", "shasum": ""}, "require": {"php": ">=7.4.0", "symfony/mailer": ">=5.4.0", "yiisoft/yii2": ">=2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\symfonymailer\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The SymfonyMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "symfony", "symfonymailer", "yii2"], "time": "2022-09-04T10:48:21+00:00"}], "packages-dev": [{"name": "craftcms/generator", "version": "1.8.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/generator/zipball/3427ee72fdf58066bbf0e3607bf8bf44f77517a2", "reference": "3427ee72fdf58066bbf0e3607bf8bf44f77517a2", "shasum": ""}, "require": {"craftcms/cms": "^4.4.11", "nette/php-generator": "^4.0", "nikic/php-parser": "^4.15", "php": "^8.0.2"}, "type": "yii2-extension", "extra": {"bootstrap": "craft\\generator\\Extension"}, "autoload": {"psr-4": {"craft\\generator\\": "src/"}}, "license": ["mit"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Craft CMS component generator", "homepage": "https://craftcms.com", "keywords": ["cms", "craftcms", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/generator/issues?state=open", "source": "https://github.com/craftcms/generator", "rss": "https://github.com/craftcms/generator/releases.atom"}, "time": "2024-06-19T14:35:36+00:00"}, {"name": "nette/php-generator", "version": "v4.1.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/690b00d81d42d5633e4457c43ef9754573b6f9d6", "reference": "690b00d81d42d5633e4457c43ef9754573b6f9d6", "shasum": ""}, "require": {"nette/utils": "^3.2.9 || ^4.0", "php": "8.0 - 8.3"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.3 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "time": "2024-05-12T17:31:02+00:00"}, {"name": "nette/utils", "version": "v4.0.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "shasum": ""}, "require": {"php": ">=8.0 <8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "time": "2024-01-17T16:50:36+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/4e1b88d21c69391150ace211e9eaf05810858d0b", "reference": "4e1b88d21c69391150ace211e9eaf05810858d0b", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2024-03-17T08:10:35+00:00"}, {"name": "psy/psysh", "version": "v0.11.22", "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/128fa1b608be651999ed9789c95e6e2a31b5802b", "reference": "128fa1b608be651999ed9789c95e6e2a31b5802b", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^4.0 || ^3.1", "php": "^8.0 || ^7.0.8", "symfony/console": "^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-0.11": "0.11.x-dev"}, "bamarni-bin": {"bin-links": false, "forward-command": false}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "time": "2023-10-14T21:56:36+00:00"}, {"name": "yiisoft/yii2-shell", "version": "2.0.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-shell/zipball/358d4651ce1f54db0f1add026c202ac2e47db06b", "reference": "358d4651ce1f54db0f1add026c202ac2e47db06b", "shasum": ""}, "require": {"psy/psysh": "~0.9.3|~0.10.3|^0.11.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0", "yiisoft/yii2": "~2.0.0"}, "type": "yii2-extension", "extra": {"bootstrap": "yii\\shell\\Bootstrap", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\shell\\": ""}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "svku<PERSON><PERSON>@gmail.com"}], "description": "The interactive shell extension for Yii framework", "keywords": ["shell", "yii2"], "time": "2022-09-04T10:37:52+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "platform-overrides": {"php": "8.0.2"}, "plugin-api-version": "2.3.0"}