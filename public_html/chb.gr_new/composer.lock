{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "6e1d96239b0b3196f28258752649e7b8", "packages": [{"name": "cakephp/core", "version": "3.9.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/core/zipball/1768817b61dbb500b7dd4b0a9e603479b7b00cc2", "reference": "1768817b61dbb500b7dd4b0a9e603479b7b00cc2", "shasum": ""}, "require": {"cakephp/utility": "^3.6.0", "php": ">=5.6.0,<8.0.0"}, "suggest": {"cakephp/cache": "To use Configure::store() and restore().", "cakephp/event": "To use PluginApplicationInterface or plugin applications."}, "type": "library", "autoload": {"psr-4": {"Cake\\Core\\": "."}, "files": ["functions.php"]}, "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/core/graphs/contributors"}], "description": "CakePHP Framework Core classes", "homepage": "https://cakephp.org", "keywords": ["cakephp", "core", "framework"], "time": "2020-10-28T18:13:14+00:00"}, {"name": "cakephp/utility", "version": "3.9.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/utility/zipball/51b0af31af3239f6141006bbd7cbc7b16aba40d6", "reference": "51b0af31af3239f6141006bbd7cbc7b16aba40d6", "shasum": ""}, "require": {"cakephp/core": "^3.6.0", "php": ">=5.6.0,<8.0.0"}, "suggest": {"ext-intl": "To use Text::transliterate() or Text::slug()", "lib-ICU": "To use Text::transliterate() or Text::slug()"}, "type": "library", "autoload": {"psr-4": {"Cake\\Utility\\": "."}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/utility/graphs/contributors"}], "description": "CakePHP Utility classes such as Inflector, String, Hash, and Security", "homepage": "https://cakephp.org", "keywords": ["cakephp", "hash", "inflector", "security", "string", "utility"], "time": "2020-12-09T02:43:02+00:00"}, {"name": "cebe/markdown", "version": "1.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "time": "2018-03-26T11:24:36+00:00"}, {"name": "composer/ca-bundle", "version": "1.2.9", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/78a0e288fdcebf92aa2318a8d3656168da6ac1a5", "reference": "78a0e288fdcebf92aa2318a8d3656168da6ac1a5", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2021-01-12T12:10:35+00:00"}, {"name": "composer/composer", "version": "1.10.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/32966a3b1d48bc01472a8321fd6472b44fad033a", "reference": "32966a3b1d48bc01472a8321fd6472b44fad033a", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/semver": "^1.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^1.1", "justinrainbow/json-schema": "^5.2.10", "php": "^5.3.2 || ^7.0", "psr/log": "^1.0", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.0", "symfony/console": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/filesystem": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/finder": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/process": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "conflict": {"symfony/console": "2.8.38"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "time": "2020-08-03T09:35:19+00:00"}, {"name": "composer/semver", "version": "1.7.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/647490bbcaf7fc4891c58f47b825eb99d19c377a", "reference": "647490bbcaf7fc4891c58f47b825eb99d19c377a", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2020-12-03T15:47:16+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/de30328a7af8680efdc03e396aad24befd513200", "reference": "de30328a7af8680efdc03e396aad24befd513200", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "time": "2020-12-03T16:04:16+00:00"}, {"name": "composer/xdebug-handler", "version": "1.4.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/f28d44c286812c714741478d968104c5e604a1d4", "reference": "f28d44c286812c714741478d968104c5e604a1d4", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "time": "2020-11-13T08:04:11+00:00"}, {"name": "craftcms/cms", "version": "3.5.18", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/cms/zipball/b307136753bac5b6adcc2e92bf56173417259f2e", "reference": "b307136753bac5b6adcc2e92bf56173417259f2e", "shasum": ""}, "require": {"composer/composer": "1.10.10", "craftcms/oauth2-craftid": "~1.0.0", "craftcms/plugin-installer": "~1.5.6", "craftcms/server-check": "~1.1.0", "creocoder/yii2-nested-sets": "~0.9.0", "elvanto/litemoji": "^1.3.1", "enshrined/svg-sanitize": "~0.13.2", "ext-curl": "*", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-zip": "*", "guzzlehttp/guzzle": ">=6.3.0 <6.5.0 || ^6.5.1", "laminas/laminas-feed": "^2.12.0", "league/flysystem": "^1.0.35", "league/oauth2-client": "^2.2.1", "mikehaertl/php-shellcommand": "^1.2.5", "mrclay/jsmin-php": "^2.4", "mrclay/minify": "^3.0.7", "php": ">=7.0.0", "pixelandtonic/imagine": "~*******", "sebastian/diff": "^2.0|^3.0|^4.0", "seld/cli-prompt": "^1.0.3", "symfony/yaml": "^3.2|^4.0", "true/punycode": "^2.1.0", "twig/twig": "~2.12.5|~2.13.1|~2.14.0", "voku/portable-utf8": "^5.4.28", "voku/stringy": "^6.2.2", "webonyx/graphql-php": "^0.12.0", "yii2tech/ar-softdelete": "^1.0.2", "yiisoft/yii2": "~********", "yiisoft/yii2-debug": "^2.1.14", "yiisoft/yii2-queue": "~2.3.0", "yiisoft/yii2-swiftmailer": "^2.1.0"}, "conflict": {"league/oauth2-client": "2.4.0"}, "provide": {"bower-asset/inputmask": "~3.2.2 | ~3.3.5", "bower-asset/jquery": "3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "1.3.*", "bower-asset/yii2-pjax": "~2.0.1"}, "suggest": {"ext-iconv": "Adds support for more character encodings than PHP’s built-in mb_convert_encoding() function, which Craft will take advantage of when converting strings to UTF-8.", "ext-imagick": "Adds support for more image processing formats and options.", "ext-intl": "Adds rich internationalization support."}, "type": "library", "autoload": {"psr-4": {"craft\\": "src/", "crafttests\\fixtures\\": "tests/fixtures/"}}, "license": ["proprietary"], "description": "Craft CMS", "homepage": "https://craftcms.com", "keywords": ["cms", "craftcms", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/cms/issues?state=open", "forum": "https://craftcms.stackexchange.com/", "source": "https://github.com/craftcms/cms", "docs": "https://craftcms.com/docs/3.x/", "rss": "https://github.com/craftcms/cms/releases.atom"}, "time": "2021-01-19T18:30:41+00:00"}, {"name": "craftcms/contact-form", "version": "2.2.7", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/contact-form/zipball/eea67de8fad2507770fdc25fe76bba32924e9e87", "reference": "5f783a22a60a64aad42077b646e1d94734dae7fb", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0-RC11"}, "type": "craft-plugin", "extra": {"name": "Contact Form", "handle": "contact-form", "documentationUrl": "https://github.com/craftcms/contact-form/blob/v2/README.md", "components": {"mailer": "craft\\contactform\\Mailer"}}, "autoload": {"psr-4": {"craft\\contactform\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Add a simple contact form to your Craft CMS site", "keywords": ["cms", "contact", "craftcms", "form", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/contact-form/issues?state=open", "source": "https://github.com/craftcms/contact-form", "docs": "https://github.com/craftcms/contact-form/blob/v2/README.md", "rss": "https://github.com/craftcms/contact-form/commits/v2.atom"}, "time": "2020-05-04T10:04:00+00:00"}, {"name": "craftcms/feed-me", "version": "4.3.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/feed-me/zipball/e391a45f028b9523d028806cf9b2ea00288b3d82", "reference": "e391a45f028b9523d028806cf9b2ea00288b3d82", "shasum": ""}, "require": {"cakephp/utility": "^3.3.12", "craftcms/cms": "^3.5.0", "jakeasmith/http_build_url": "^1.0", "league/csv": "^8.2 || ^9.0", "nesbot/carbon": "^1.22 || ^2.10", "ralouphie/mimey": "^2.1.0", "seld/jsonlint": "^1.7"}, "type": "craft-plugin", "extra": {"name": "Feed Me", "handle": "feed-me", "documentationUrl": "https://docs.craftcms.com/feed-me/v4/"}, "autoload": {"psr-4": {"craft\\feedme\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Import content from XML, RSS, CSV or JSON feeds into entries, categories, Craft Commerce products, and more.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "feed me"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/feed-me/issues?state=open", "source": "https://github.com/craftcms/feed-me", "docs": "https://docs.craftcms.com/feed-me/v4/", "rss": "https://github.com/craftcms/feed-me/commits/master.atom"}, "time": "2020-12-14T22:33:11+00:00"}, {"name": "craftcms/oauth2-craftid", "version": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/oauth2-craftid/zipball/3f18364139d72d83fb50546d85130beaaa868836", "reference": "3f18364139d72d83fb50546d85130beaaa868836", "shasum": ""}, "require": {"league/oauth2-client": "^2.2.1"}, "require-dev": {"phpunit/phpunit": "^5.0", "satooshi/php-coveralls": "^1.0", "squizlabs/php_codesniffer": "^2.0"}, "type": "library", "autoload": {"psr-4": {"craftcms\\oauth2\\client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Craft OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["Authentication", "authorization", "client", "cms", "craftcms", "craftid", "o<PERSON>h", "oauth2"], "time": "2017-11-22T19:46:18+00:00"}, {"name": "craftcms/plugin-installer", "version": "1.5.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/plugin-installer/zipball/4d24e227a086db3d8784a705f59060c49cf989ad", "reference": "4d24e227a086db3d8784a705f59060c49cf989ad", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4"}, "type": "composer-plugin", "extra": {"class": "craft\\composer\\Plugin"}, "autoload": {"psr-4": {"craft\\composer\\": "src/"}}, "license": ["MIT"], "description": "Craft CMS Plugin Installer", "homepage": "https://craftcms.com/", "keywords": ["cms", "composer", "craftcms", "installer", "plugin"], "time": "2020-10-26T23:34:21+00:00"}, {"name": "craftcms/redactor", "version": "2.8.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/redactor/zipball/baa05090b17b9b631ed3eb9f0687f48947d7dbc2", "reference": "baa05090b17b9b631ed3eb9f0687f48947d7dbc2", "shasum": ""}, "require": {"craftcms/cms": "^3.5.0"}, "type": "craft-plugin", "extra": {"name": "Redactor", "handle": "redactor", "documentationUrl": "https://github.com/craftcms/redactor/blob/v2/README.md"}, "autoload": {"psr-4": {"craft\\redactor\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Edit rich text content in Craft CMS using Redactor by <PERSON>mperavi.", "keywords": ["cms", "craftcms", "html", "redactor", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/redactor/issues?state=open", "source": "https://github.com/craftcms/redactor", "docs": "https://github.com/craftcms/redactor/blob/v2/README.md", "rss": "https://github.com/craftcms/redactor/commits/v2.atom"}, "time": "2020-12-08T12:55:30+00:00"}, {"name": "craftcms/server-check", "version": "1.1.9", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/server-check/zipball/579fd9ac93580800330695c5d38ca6decb6601ac", "reference": "0f445599b3a2ccbeba4a83cf34a8070f56326909", "shasum": ""}, "type": "library", "autoload": {"classmap": ["server/requirements"]}, "license": ["MIT"], "description": "Craft CMS Server Check", "homepage": "https://craftcms.com/", "keywords": ["cms", "craftcms", "requirements", "yii2"], "time": "2020-05-28T16:57:05+00:00"}, {"name": "creocoder/yii2-nested-sets", "version": "0.9.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/creocoder/yii2-nested-sets/zipball/cb8635a459b6246e5a144f096b992dcc30cf9954", "reference": "cb8635a459b6246e5a144f096b992dcc30cf9954", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"creocoder\\nestedsets\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The nested sets behavior for the Yii framework", "keywords": ["nested sets", "yii2"], "time": "2015-01-27T10:53:51+00:00"}, {"name": "defuse/php-encryption", "version": "v2.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/0f407c43b953d571421e0020ba92082ed5fb7620", "reference": "0f407c43b953d571421e0020ba92082ed5fb7620", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.4.0"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "time": "2018-07-24T23:27:56+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/lexer", "version": "1.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/1febd6c3ef84253d7c815bed85fc622ad207a9f8", "reference": "1febd6c3ef84253d7c815bed85fc622ad207a9f8", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "^4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2019-06-08T11:03:04+00:00"}, {"name": "doublesecretagency/craft-siteswitcher", "version": "2.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/doublesecretagency/craft-siteswitcher/zipball/7410568df912755f9c53b3f19841c3103525b1b0", "reference": "7410568df912755f9c53b3f19841c3103525b1b0", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0"}, "type": "craft-plugin", "extra": {"name": "Site Switcher", "handle": "site-switcher", "schemaVersion": "0.0.0", "changelogUrl": "https://raw.githubusercontent.com/doublesecretagency/craft-siteswitcher/v2/CHANGELOG.md", "class": "doublesecretagency\\siteswitcher\\SiteSwitcher"}, "autoload": {"psr-4": {"doublesecretagency\\siteswitcher\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Double Secret Agency", "homepage": "https://www.doublesecretagency.com/plugins"}], "description": "Easily switch between sites on any page of your website.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "language-link", "language-switcher", "languages", "locales", "multi-site", "multisite", "sites", "switcher"], "support": {"docs": "https://github.com/doublesecretagency/craft-siteswitcher/blob/v2/README.md", "issues": "https://github.com/doublesecretagency/craft-siteswitcher/issues"}, "time": "2020-12-02T00:37:00+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2020-12-29T14:50:06+00:00"}, {"name": "elvanto/litemoji", "version": "1.4.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/elvanto/litemoji/zipball/17bf635e4d1a5b4d35d2cadf153cd589b78af7f0", "reference": "17bf635e4d1a5b4d35d2cadf153cd589b78af7f0", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"milesj/emojibase": "3.1.0", "phpunit/phpunit": "^5.0"}, "type": "library", "autoload": {"psr-4": {"LitEmoji\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library simplifying the conversion of unicode, HTML and shortcode emoji.", "keywords": ["emoji", "php-emoji"], "time": "2018-09-28T05:23:38+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.13.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/bc66593f255b7d2613d8f22041180036979b6403", "reference": "bc66593f255b7d2613d8f22041180036979b6403", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "time": "2020-01-20T01:34:17+00:00"}, {"name": "ether/logs", "version": "3.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/ethercreative/logs/zipball/dd2861b3b76a8cd9a49e31d6756292218ca6fae9", "reference": "dd2861b3b76a8cd9a49e31d6756292218ca6fae9", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0-RC1"}, "type": "craft-plugin", "extra": {"handle": "logs", "name": "Logs", "developer": "<PERSON>ther <PERSON>", "developerUrl": "https://ethercreative.co.uk", "class": "ether\\logs\\Logs", "schemaVersion": "3.0.0"}, "autoload": {"psr-4": {"ether\\logs\\": "src/"}}, "description": "Access logs from the CP", "support": {"issues": "https://github.com/ethercreative/logs"}, "time": "2019-11-25T16:59:18+00:00"}, {"name": "ether/seo", "version": "3.6.7", "dist": {"type": "zip", "url": "https://api.github.com/repos/ethercreative/seo/zipball/9e545940248a6b70b16adf3480b9c8f6c197859d", "reference": "9e545940248a6b70b16adf3480b9c8f6c197859d", "shasum": ""}, "require": {"craftcms/cms": "^3.2.0"}, "type": "craft-plugin", "extra": {"handle": "seo", "name": "SEO", "developer": "<PERSON>ther <PERSON>", "developerUrl": "https://ethercreative.co.uk", "class": "ether\\seo\\Seo"}, "autoload": {"psr-4": {"ether\\seo\\": "src/"}}, "license": ["MIT"], "description": "SEO utilities including a unique field type, sitemap, & redirect manager", "support": {"issues": "https://github.com/ethercreative/seo", "docs": "https://github.com/ethercreative/seo/blob/v3/README.md"}, "time": "2020-11-30T12:51:47+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.13.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "shasum": ""}, "require": {"php": ">=5.2"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2020-06-29T00:56:53+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2020-06-16T21:01:06+00:00"}, {"name": "guzzlehttp/promises", "version": "1.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/60d379c243457e073cff02bc323a2a86cb355631", "reference": "60d379c243457e073cff02bc323a2a86cb355631", "shasum": ""}, "require": {"php": ">=5.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2020-09-30T07:37:28+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.7.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/53330f47520498c0ae1f61f7e2c90f55690c06a3", "reference": "53330f47520498c0ae1f61f7e2c90f55690c06a3", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2020-09-30T07:37:11+00:00"}, {"name": "intervention/httpauth", "version": "2.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/httpauth/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"laravel": {"providers": ["Intervention\\Httpauth\\HttpauthServiceProvider"], "aliases": {"Httpauth": "Intervention\\Httpauth\\Facades\\Httpauth"}}}, "autoload": {"psr-4": {"Intervention\\Httpauth\\": "src/Intervention/Httpauth"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://olivervogel.com/"}], "description": "HTTP authentication (Basic & Digest) including ServiceProviders for easy Laravel integration", "homepage": "https://github.com/Intervention/httpauth", "keywords": ["Authentication", "http", "laravel"], "time": "2019-09-09T11:59:51+00:00"}, {"name": "jakeasmith/http_build_url", "version": "1.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/jakeasmith/http_build_url/zipball/93c273e77cb1edead0cf8bcf8cd2003428e74e37", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37", "shasum": ""}, "type": "library", "autoload": {"files": ["src/http_build_url.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality for http_build_url() to environments without pecl_http.", "time": "2017-05-01T15:36:40+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b", "reference": "2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b", "shasum": ""}, "require": {"php": ">=5.3.3"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2020-05-27T16:41:55+00:00"}, {"name": "kylekatarnls/update-helper", "version": "1.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/kylekatarnls/update-helper/zipball/429be50660ed8a196e0798e5939760f168ec8ce9", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0.0", "php": ">=5.3.0"}, "type": "composer-plugin", "extra": {"class": "UpdateHelper\\ComposerPlugin"}, "autoload": {"psr-0": {"UpdateHelper\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Update helper", "time": "2020-04-07T20:44:10+00:00"}, {"name": "laminas/laminas-escaper", "version": "2.6.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-escaper/zipball/25f2a053eadfa92ddacb609dcbbc39362610da70", "reference": "25f2a053eadfa92ddacb609dcbbc39362610da70", "shasum": ""}, "require": {"laminas/laminas-zendframework-bridge": "^1.0", "php": "^5.6 || ^7.0"}, "replace": {"zendframework/zend-escaper": "self.version"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6.x-dev", "dev-develop": "2.7.x-dev"}}, "autoload": {"psr-4": {"Laminas\\Escaper\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "homepage": "https://laminas.dev", "keywords": ["escaper", "laminas"], "time": "2019-12-31T16:43:30+00:00"}, {"name": "laminas/laminas-feed", "version": "2.12.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-feed/zipball/3c91415633cb1be6f9d78683d69b7dcbfe6b4012", "reference": "3c91415633cb1be6f9d78683d69b7dcbfe6b4012", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "laminas/laminas-escaper": "^2.5.2", "laminas/laminas-stdlib": "^3.2.1", "laminas/laminas-zendframework-bridge": "^1.0", "php": "^5.6 || ^7.0"}, "replace": {"zendframework/zend-feed": "^2.12.0"}, "suggest": {"laminas/laminas-cache": "Laminas\\Cache component, for optionally caching feeds between requests", "laminas/laminas-db": "Laminas\\Db component, for use with PubSubHubbub", "laminas/laminas-http": "Laminas\\Http for PubSubHubbub, and optionally for use with Laminas\\Feed\\Reader", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component, for easily extending ExtensionManager implementations", "laminas/laminas-validator": "Laminas\\Validator component, for validating email addresses used in Atom feeds and entries when using the Writer subcomponent", "psr/http-message": "PSR-7 ^1.0.1, if you wish to use Laminas\\Feed\\Reader\\Http\\Psr7ResponseDecorator"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.12.x-dev", "dev-develop": "2.13.x-dev"}}, "autoload": {"psr-4": {"Laminas\\Feed\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides functionality for consuming RSS and Atom feeds", "homepage": "https://laminas.dev", "keywords": ["feed", "laminas"], "time": "2020-08-18T13:45:04+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/2b18347625a2f06a1a485acfbc870f699dbe51c6", "reference": "2b18347625a2f06a1a485acfbc870f699dbe51c6", "shasum": ""}, "require": {"laminas/laminas-zendframework-bridge": "^1.0", "php": "^5.6 || ^7.0"}, "replace": {"zendframework/zend-stdlib": "self.version"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev", "dev-develop": "3.3.x-dev"}}, "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "time": "2019-12-31T17:51:15+00:00"}, {"name": "laminas/laminas-zendframework-bridge", "version": "1.1.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/6ede70583e101030bcace4dcddd648f760ddf642", "reference": "6ede70583e101030bcace4dcddd648f760ddf642", "shasum": ""}, "require": {"php": "^5.6 || ^7.0 || ^8.0"}, "type": "library", "extra": {"laminas": {"module": "Laminas\\ZendFrameworkBridge"}}, "autoload": {"files": ["src/autoload.php"], "psr-4": {"Laminas\\ZendFrameworkBridge\\": "src//"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "Alias legacy ZF class names to Laminas Project equivalents.", "keywords": ["ZendFramework", "autoloading", "laminas", "zf"], "time": "2020-09-14T14:23:00+00:00"}, {"name": "league/csv", "version": "8.2.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/d2aab1e7bde802582c3879acf03d92716577c76d", "reference": "d2aab1e7bde802582c3879acf03d92716577c76d", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.2-dev"}}, "autoload": {"psr-4": {"League\\Csv\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "Csv data manipulation made easy in PHP", "homepage": "http://csv.thephpleague.com", "keywords": ["csv", "export", "filter", "import", "read", "write"], "time": "2018-02-06T08:27:03+00:00"}, {"name": "league/flysystem", "version": "1.0.70", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/585824702f534f8d3cf7fab7225e8466cc4b7493", "reference": "585824702f534f8d3cf7fab7225e8466cc4b7493", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": ">=5.5.9"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "time": "2020-07-26T07:20:36+00:00"}, {"name": "league/oauth2-client", "version": "2.6.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/badb01e62383430706433191b82506b6df24ad98", "reference": "badb01e62383430706433191b82506b6df24ad98", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "paragonie/random_compat": "^1 || ^2 || ^9.99", "php": "^5.6 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.0.x-dev"}}, "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "time": "2020-10-28T02:03:40+00:00"}, {"name": "marcusschwarz/lesserphp", "version": "v0.5.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/MarcusSchwarz/lesserphp/zipball/3a0f5ae0d63cbb661b5f4afd2f96875e73b3ad7e", "reference": "3a0f5ae0d63cbb661b5f4afd2f96875e73b3ad7e", "shasum": ""}, "bin": ["plessc"], "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.1-dev"}}, "autoload": {"classmap": ["lessc.inc.php"]}, "license": ["MIT", "GPL-3.0"], "authors": [{"name": "Leaf Corcoran", "email": "<EMAIL>", "homepage": "http://leafo.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maswaba.de"}], "description": "lesserphp is a compiler for LESS written in PHP based on leaf<PERSON>'s lessphp.", "homepage": "http://leafo.net/lessphp/", "time": "2020-01-19T19:18:49+00:00"}, {"name": "masterminds/html5", "version": "2.7.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/104443ad663d15981225f99532ba73c2f1d6b6f2", "reference": "104443ad663d15981225f99532ba73c2f1d6b6f2", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-libxml": "*", "php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "time": "2019-07-25T07:03:26+00:00"}, {"name": "michelf/php-smartypants", "version": "1.8.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/michelf/php-smartypants/zipball/47d17c90a4dfd0ccf1f87e25c65e6c8012415aad", "reference": "47d17c90a4dfd0ccf1f87e25c65e6c8012415aad", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"Michelf": ""}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://michelf.ca/", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://daringfireball.net/"}], "description": "PHP SmartyPants", "homepage": "https://michelf.ca/projects/php-smartypants/", "keywords": ["dashes", "quotes", "spaces", "typographer", "typography"], "time": "2016-12-13T01:01:17+00:00"}, {"name": "mikehaertl/php-shellcommand", "version": "1.6.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-shellcommand/zipball/fe86ec847877b83bf61a96719e7f2e3b3e516a6b", "reference": "fe86ec847877b83bf61a96719e7f2e3b3e516a6b", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "type": "library", "autoload": {"psr-4": {"mikehaertl\\shellcommand\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An object oriented interface to shell commands", "keywords": ["shell"], "time": "2020-11-23T17:31:15+00:00"}, {"name": "monolog/monolog", "version": "1.26.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/2209ddd84e7ef1256b7af205d0717fb62cfc9c33", "reference": "2209ddd84e7ef1256b7af205d0717fb62cfc9c33", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2020-12-14T12:56:38+00:00"}, {"name": "mrclay/jsmin-php", "version": "2.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/mrclay/jsmin-php/zipball/bb05febc9440852d39899255afd5569b7f21a72c", "reference": "bb05febc9440852d39899255afd5569b7f21a72c", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"JSMin\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Provides a modified port of <PERSON>'s jsmin.c, which removes unnecessary whitespace from JavaScript files.", "homepage": "https://github.com/mrclay/jsmin-php/", "keywords": ["compress", "jsmin", "minify"], "time": "2018-12-06T15:03:38+00:00"}, {"name": "mrclay/minify", "version": "3.0.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/mrclay/minify/zipball/8dba84a2d24ae6382057a1215ad3af25202addb9", "reference": "8dba84a2d24ae6382057a1215ad3af25202addb9", "shasum": ""}, "require": {"ext-pcre": "*", "intervention/httpauth": "^2.0|^3.0", "marcusschwarz/lesserphp": "^0.5.1", "monolog/monolog": "~1.1|~2.0", "mrclay/jsmin-php": "~2", "mrclay/props-dic": "^2.2|^3.0", "php": "^5.3.0 || ^7.0", "tubalmartin/cssmin": "~4"}, "suggest": {"firephp/firephp-core": "Use FirePHP for Log messages", "meenie/javascript-packer": "Keep track of the Packer PHP port using Composer"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["lib/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Minify is a PHP app that helps you follow several rules for client-side performance. It combines multiple CSS or Javascript files, removes unnecessary whitespace and comments, and serves them with gzip encoding and optimal client-side cache headers", "homepage": "https://github.com/mrclay/minify", "time": "2020-04-02T19:47:26+00:00"}, {"name": "mrclay/props-dic", "version": "3.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/mrclay/Props/zipball/0b0fd254e33e2d60bc2bcd7867f2ab3cdd05a843", "reference": "0b0fd254e33e2d60bc2bcd7867f2ab3cdd05a843", "shasum": ""}, "require": {"php": ">=5.3.3", "pimple/pimple": "~3.0", "psr/container": "^1.0"}, "type": "library", "autoload": {"psr-0": {"Props\\": ["src/"]}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.mrclay.org/"}], "description": "Props is a simple DI container that allows retrieving values via custom property and method names", "keywords": ["container", "dependency injection", "dependency injection container", "di", "di container"], "time": "2019-11-26T17:56:10+00:00"}, {"name": "mundschenk-at/php-typography", "version": "v6.5.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/mundschenk-at/php-typography/zipball/ce039dc8826038f898e5338e5df1c4bafddc0c6e", "reference": "ce039dc8826038f898e5338e5df1c4bafddc0c6e", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-pcre": "*", "masterminds/html5": "^2.5.0", "php": ">=5.6.0"}, "bin": ["src/bin/update-patterns.php", "src/bin/update-iana.php"], "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://code.mundschenk.at", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kingdesk.com", "role": "Original author"}], "description": "A PHP library for improving your web typography", "time": "2019-12-07T12:39:54+00:00"}, {"name": "nesbot/carbon", "version": "1.39.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/4be0c005164249208ce1b5ca633cd57bdd42ff33", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33", "shasum": ""}, "require": {"kylekatarnls/update-helper": "^1.1", "php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "bin": ["bin/upgrade-carbon"], "type": "library", "extra": {"update-helper": "Carbon\\Upgrade", "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2019-10-14T05:51:36+00:00"}, {"name": "nikic/php-parser", "version": "v4.7.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/21dce06dfbf0365c6a7cc8fdbdc995926c6a9300", "reference": "21dce06dfbf0365c6a7cc8fdbdc995926c6a9300", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.7-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2020-07-25T13:18:53+00:00"}, {"name": "nystudio107/craft-typogrify", "version": "1.1.18", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-typogrify/zipball/28896d37f184cef87d2bbd84833565d0d732e331", "reference": "28896d37f184cef87d2bbd84833565d0d732e331", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0", "michelf/php-smartypants": "^1.8", "mundschenk-at/php-typography": "^6.0"}, "type": "craft-plugin", "extra": {"name": "Typogrify", "handle": "typogrify", "schemaVersion": "1.0.0", "hasCpSettings": false, "hasCpSection": false, "changelogUrl": "https://raw.githubusercontent.com/nystudio107/craft-typogrify/v1/CHANGELOG.md", "components": {"typogrify": "nystudio107\\typogrify\\services\\TypogrifyService"}, "class": "nystudio107\\typogrify\\Typogrify"}, "autoload": {"psr-4": {"nystudio107\\typogrify\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com/"}], "description": "Typogrify prettifies your web typography by preventing ugly quotes and 'widows' and more", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "typogrify"], "support": {"docs": "https://github.com/nystudio107/craft-typogrify/blob/v1/README.md", "issues": "https://github.com/nystudio107/craft-typogrify/issues"}, "time": "2019-04-30T21:24:04+00:00"}, {"name": "opis/closure", "version": "3.6.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5", "reference": "943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "time": "2020-11-07T02:01:34+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2020-10-15T08:29:30+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "1.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2017-09-11T18:02:19+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/da3fd972d6bafd628114f7e7e036f45944b62e9c", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0 || ^2.0.0", "phpdocumentor/type-resolver": "~0.4 || ^1.0.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.5", "mockery/mockery": "^1.0", "phpdocumentor/type-resolver": "0.4.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2019-12-28T18:55:12+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "0.5.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/cf842904952e64e703800d094cdf34e715a8a3ae", "reference": "cf842904952e64e703800d094cdf34e715a8a3ae", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2017-12-30T13:23:38+00:00"}, {"name": "phpoption/phpoption", "version": "1.7.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/77f7c4d2e65413aff5b5a8cc8b3caf7a28d81959", "reference": "77f7c4d2e65413aff5b5a8cc8b3caf7a28d81959", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.3", "phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "time": "2019-12-15T19:35:24+00:00"}, {"name": "pimple/pimple", "version": "v3.2.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/9e403941ef9d65d20cba7d54e29fe906db42cf32", "reference": "9e403941ef9d65d20cba7d54e29fe906db42cf32", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/container": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"psr-0": {"Pimple": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container", "homepage": "http://pimple.sensiolabs.org", "keywords": ["container", "dependency injection"], "time": "2018-01-21T07:42:36+00:00"}, {"name": "pixelandtonic/imagine", "version": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/pixelandtonic/Imagine/zipball/2bedcf2dfab50a22126498a16241944c9d4cde65", "reference": "2bedcf2dfab50a22126498a16241944c9d4cde65", "shasum": ""}, "require": {"php": ">=5.3.2"}, "suggest": {"ext-gd": "to use the GD implementation", "ext-gmagick": "to use the Gmagick implementation", "ext-imagick": "to use the Imagick implementation"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "autoload": {"psr-4": {"Imagine\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://avalanche123.com"}], "description": "Image processing for PHP 5.3", "homepage": "http://imagine.readthedocs.org/", "keywords": ["drawing", "graphics", "image manipulation", "image processing"], "time": "2020-11-05T18:36:18+00:00"}, {"name": "psr/container", "version": "1.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psy/psysh", "version": "v0.10.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/a8aec1b2981ab66882a01cce36a49b6317dc3560", "reference": "a8aec1b2981ab66882a01cce36a49b6317dc3560", "shasum": ""}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3", "php": "^8.0 || ^7.0 || ^5.5.9", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "time": "2020-05-03T19:32:03+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "ralouphie/mimey", "version": "2.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/mimey/zipball/8f74e6da73f9df7bd965e4e123f3d8fb9acb89ba", "reference": "8f74e6da73f9df7bd965e4e123f3d8fb9acb89ba", "shasum": ""}, "require": {"php": "^5.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Mimey\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP package for converting file extensions to MIME types and vice versa.", "time": "2019-03-08T08:49:03+00:00"}, {"name": "sebastian/diff", "version": "2.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/347c1d8b49c5c3ee30c7040ea6fc446790e6bddd", "reference": "347c1d8b49c5c3ee30c7040ea6fc446790e6bddd", "shasum": ""}, "require": {"php": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-08-03T08:09:46+00:00"}, {"name": "seld/cli-prompt", "version": "1.0.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/cli-prompt/zipball/b8dfcf02094b8c03b40322c229493bb2884423c5", "reference": "b8dfcf02094b8c03b40322c229493bb2884423c5", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\CliPrompt\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Allows you to prompt for user input on the command line, and optionally hide the characters they type", "keywords": ["cli", "console", "hidden", "input", "prompt"], "time": "2020-12-15T21:32:01+00:00"}, {"name": "seld/jsonlint", "version": "1.8.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/9ad6ce79c342fbd44df10ea95511a1b24dee5b57", "reference": "9ad6ce79c342fbd44df10ea95511a1b24dee5b57", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2020-11-11T09:19:24+00:00"}, {"name": "seld/phar-utils", "version": "1.1.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/8674b1d84ffb47cc59a101f5d5a3b61e87d23796", "reference": "8674b1d84ffb47cc59a101f5d5a3b61e87d23796", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "time": "2020-07-07T18:42:57+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/698a6a9f54d7eb321274de3ad19863802c879fb7", "reference": "698a6a9f54d7eb321274de3ad19863802c879fb7", "shasum": ""}, "require": {"egulias/email-validator": "^2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "time": "2021-01-12T09:35:59+00:00"}, {"name": "symfony/console", "version": "v3.3.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/b0878233cb5c4391347e5495089c7af11b8e6201", "reference": "b0878233cb5c4391347e5495089c7af11b8e6201", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/debug": "~2.8|~3.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3", "symfony/dependency-injection": "~3.3", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/filesystem": "~2.8|~3.0", "symfony/http-kernel": "~2.8|~3.0", "symfony/process": "~2.8|~3.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/filesystem": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2017-07-29T21:27:59+00:00"}, {"name": "symfony/debug", "version": "v3.3.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/7c13ae8ce1e2adbbd574fc39de7be498e1284e13", "reference": "7c13ae8ce1e2adbbd574fc39de7be498e1284e13", "shasum": ""}, "require": {"php": ">=5.5.9", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2017-07-28T15:27:31+00:00"}, {"name": "symfony/filesystem", "version": "v3.3.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/427987eb4eed764c3b6e38d52a0f87989e010676", "reference": "427987eb4eed764c3b6e38d52a0f87989e010676", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2017-07-11T07:17:58+00:00"}, {"name": "symfony/finder", "version": "v3.3.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/baea7f66d30854ad32988c11a09d7ffd485810c4", "reference": "baea7f66d30854ad32988c11a09d7ffd485810c4", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2017-06-01T21:01:25+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/aed596913b70fae57be53d86faa2e9ef85a2297b", "reference": "aed596913b70fae57be53d86faa2e9ef85a2297b", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/085241787d52fa6f7a774fd034135fef0cfd5496", "reference": "085241787d52fa6f7a774fd034135fef0cfd5496", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/64fbe93b02024763359aea2bc81af05086c6af82", "reference": "64fbe93b02024763359aea2bc81af05086c6af82", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/4ad5115c0f5d5172a9fe8147675ec6de266d8826", "reference": "4ad5115c0f5d5172a9fe8147675ec6de266d8826", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php70": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2020-10-21T09:57:48+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8db0ae7936b42feb370840cf24de1a144fb0ef27", "reference": "8db0ae7936b42feb370840cf24de1a144fb0ef27", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/b5f7b932ee6fa802fc792eabd77c4c88084517ce", "reference": "b5f7b932ee6fa802fc792eabd77c4c88084517ce", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/3fe414077251a81a1b15b1c709faf5c2fbae3d4e", "reference": "3fe414077251a81a1b15b1c709faf5c2fbae3d4e", "shasum": ""}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/beecef6b463b06954638f02378f52496cb84bacc", "reference": "beecef6b463b06954638f02378f52496cb84bacc", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/process", "version": "v3.3.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/07432804942b9f6dd7b7377faf9920af5f95d70a", "reference": "07432804942b9f6dd7b7377faf9920af5f95d70a", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2017-07-13T13:05:09+00:00"}, {"name": "symfony/translation", "version": "v3.3.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/35dd5fb003c90e8bd4d8cabdf94bf9c96d06fdc3", "reference": "35dd5fb003c90e8bd4d8cabdf94bf9c96d06fdc3", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/yaml": "<3.3"}, "suggest": {"psr/log": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2017-06-24T16:45:30+00:00"}, {"name": "symfony/var-dumper", "version": "v3.3.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/b2623bccb969ad595c2090f9be498b74670d0663", "reference": "b2623bccb969ad595c2090f9be498b74670d0663", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-symfony_debug": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2017-07-28T06:06:09+00:00"}, {"name": "symfony/yaml", "version": "v3.3.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/ddc23324e6cfe066f3dd34a37ff494fa80b617ed", "reference": "ddc23324e6cfe066f3dd34a37ff494fa80b617ed", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"symfony/console": "~2.8|~3.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2017-07-23T12:43:26+00:00"}, {"name": "true/punycode", "version": "v2.1.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/true/php-punycode/zipball/a4d0c11a36dd7f4e7cd7096076cab6d3378a071e", "reference": "a4d0c11a36dd7f4e7cd7096076cab6d3378a071e", "shasum": ""}, "require": {"php": ">=5.3.0", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpunit/phpunit": "~4.7", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "autoload": {"psr-4": {"TrueBV\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>an <PERSON>", "email": "<EMAIL>"}], "description": "A Bootstring encoding of Unicode for Internationalized Domain Names in Applications (IDNA)", "homepage": "https://github.com/true/php-punycode", "keywords": ["idna", "punycode"], "time": "2016-11-16T10:37:54+00:00"}, {"name": "tubalmartin/cssmin", "version": "v4.1.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/tubalmartin/YUI-CSS-compressor-PHP-port/zipball/3cbf557f4079d83a06f9c3ff9b957c022d7805cf", "reference": "3cbf557f4079d83a06f9c3ff9b957c022d7805cf", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3.2"}, "bin": ["cssmin"], "type": "library", "autoload": {"psr-4": {"tubalmartin\\CssMin\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://tubalmartin.me/"}], "description": "A PHP port of the YUI CSS compressor", "homepage": "https://github.com/tubalmartin/YUI-CSS-compressor-PHP-port", "keywords": ["compress", "compressor", "css", "cssmin", "minify", "yui"], "time": "2018-01-15T15:26:51+00:00"}, {"name": "twig/twig", "version": "v2.12.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/18772e0190734944277ee97a02a9a6c6555fcd94", "reference": "18772e0190734944277ee97a02a9a6c6555fcd94", "shasum": ""}, "require": {"php": "^7.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.12-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2020-02-11T15:31:23+00:00"}, {"name": "vlucas/phpdotenv", "version": "v3.6.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/1bdf24f065975594f6a117f0f1f6cabf1333b156", "reference": "1bdf24f065975594f6a117f0f1f6cabf1333b156", "shasum": ""}, "require": {"php": "^5.4 || ^7.0", "phpoption/phpoption": "^1.5", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2019-09-10T21:37:39+00:00"}, {"name": "voku/anti-xss", "version": "4.1.31", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/anti-xss/zipball/22dea9be8dbffa466995ea87a12da5f3bce874bb", "reference": "22dea9be8dbffa466995ea87a12da5f3bce874bb", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-utf8": "~5.4.51"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["MIT"], "authors": [{"name": "EllisLab Dev Team", "homepage": "http://ellislab.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.moelleken.org/"}], "description": "anti xss-library", "homepage": "https://github.com/voku/anti-xss", "keywords": ["anti-xss", "clean", "security", "xss"], "time": "2020-12-02T02:10:30+00:00"}, {"name": "voku/arrayy", "version": "7.8.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Arrayy/zipball/eacd7fa54f8584ffe919a12d11093b0516081ecf", "reference": "eacd7fa54f8584ffe919a12d11093b0516081ecf", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.0.0", "phpdocumentor/reflection-docblock": "~4.3 || ~5.0", "symfony/polyfill-mbstring": "~1.0"}, "type": "library", "autoload": {"psr-4": {"Arrayy\\": "src/"}, "files": ["src/Create.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.moelleken.org/", "role": "Maintainer"}], "description": "Array manipulation library for PHP, called Arrayy!", "keywords": ["A<PERSON>yy", "array", "helpers", "manipulation", "methods", "utility", "utils"], "time": "2020-11-02T22:37:00+00:00"}, {"name": "voku/email-check", "version": "3.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/email-check/zipball/f91fc9da57fbb29c4ded5a1fc1238d4b988758dd", "reference": "f91fc9da57fbb29c4ded5a1fc1238d4b988758dd", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-intl-idn": "~1.10"}, "require-dev": {"fzaninotto/faker": "~1.7", "phpunit/phpunit": "~6.0 || ~7.0"}, "suggest": {"ext-intl": "Use Intl for best performance"}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "email-check (syntax, dns, trash, ...) library", "homepage": "https://github.com/voku/email-check", "keywords": ["check-email", "email", "mail", "mail-check", "validate-email", "validate-email-address", "validate-mail"], "time": "2019-01-02T23:08:14+00:00"}, {"name": "voku/portable-ascii", "version": "1.5.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/80953678b19901e5165c56752d087fc11526017c", "reference": "80953678b19901e5165c56752d087fc11526017c", "shasum": ""}, "require": {"php": ">=7.0.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "time": "2020-11-12T00:07:28+00:00"}, {"name": "voku/portable-utf8", "version": "5.4.51", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-utf8/zipball/578f5266725dc9880483d24ad0cfb39f8ce170f7", "reference": "578f5266725dc9880483d24ad0cfb39f8ce170f7", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-iconv": "~1.0", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.0", "voku/portable-ascii": "~1.5.6"}, "suggest": {"ext-ctype": "Use Ctype for e.g. hexadecimal digit detection", "ext-fileinfo": "Use Fileinfo for better binary file detection", "ext-iconv": "Use iconv for best performance", "ext-intl": "Use Intl for best performance", "ext-json": "Use JSON for string detection", "ext-mbstring": "Use Mbstring for best performance"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}, "files": ["bootstrap.php"]}, "license": ["(Apache-2.0 or GPL-2.0)"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "http://pageconfig.com/"}, {"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable UTF-8 library - performance optimized (unicode) string functions for php.", "homepage": "https://github.com/voku/portable-utf8", "keywords": ["UTF", "clean", "php", "unicode", "utf-8", "utf8"], "time": "2020-12-02T01:58:49+00:00"}, {"name": "voku/stop-words", "version": "2.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/stop-words/zipball/8e63c0af20f800b1600783764e0ce19e53969f71", "reference": "8e63c0af20f800b1600783764e0ce19e53969f71", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Stop-Words via PHP", "keywords": ["stop words", "stop-words"], "time": "2018-11-23T01:37:27+00:00"}, {"name": "voku/stringy", "version": "6.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Stringy/zipball/b534a95b58b020faf3e3b779abc0bc277b5fb83c", "reference": "b534a95b58b020faf3e3b779abc0bc277b5fb83c", "shasum": ""}, "require": {"defuse/php-encryption": "~2.0", "ext-json": "*", "php": ">=7.0.0", "voku/anti-xss": "~4.1", "voku/arrayy": "~7.5", "voku/email-check": "~3.0", "voku/portable-ascii": "~1.5", "voku/portable-utf8": "~5.4", "voku/urlify": "~5.0"}, "replace": {"danielstjules/stringy": "~3.0"}, "type": "library", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.moelleken.org/", "role": "Fork-Maintainer"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2020-09-27T21:32:36+00:00"}, {"name": "voku/urlify", "version": "5.0.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/urlify/zipball/d59bfa6d13ce08062e2fe40dd23d226262f961c5", "reference": "d59bfa6d13ce08062e2fe40dd23d226262f961c5", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-ascii": "~1.4", "voku/portable-utf8": "~5.4", "voku/stop-words": "~2.0"}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.johnnybroadway.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://moelleken.org/"}], "description": "PHP port of URLify.js from the Django project. Transliterates non-ascii characters for use in URLs.", "homepage": "https://github.com/voku/urlify", "keywords": ["encode", "iconv", "link", "slug", "translit", "transliterate", "transliteration", "url", "urlify"], "time": "2019-12-13T02:57:54+00:00"}, {"name": "webmozart/assert", "version": "1.9.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/bafc69caeb4d49c39fd0779086c03a3738cbb389", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<3.9.1"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2020-07-08T17:02:28+00:00"}, {"name": "webonyx/graphql-php", "version": "v0.12.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/4c545e5ec4fc37f6eb36c19f5a0e7feaf5979c95", "reference": "4c545e5ec4fc37f6eb36c19f5a0e7feaf5979c95", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^4.8", "psr/http-message": "^1.0", "react/promise": "2.*"}, "suggest": {"psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "type": "library", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "time": "2018-09-02T14:59:54+00:00"}, {"name": "yii2tech/ar-softdelete", "version": "1.0.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/yii2tech/ar-softdelete/zipball/498ed03f89ded835f0ca156ec50d432191c58769", "reference": "498ed03f89ded835f0ca156ec50d432191c58769", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.13"}, "require-dev": {"phpunit/phpunit": "4.8.27|^5.0|^6.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"yii2tech\\ar\\softdelete\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides support for ActiveRecord soft delete in Yii2", "keywords": ["active", "delete", "integrity", "record", "smart", "soft", "yii2"], "time": "2019-07-30T11:05:57+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.38", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/fd01e747cc66a049ec105048f0ab8dfbdf60bf4b", "reference": "fd01e747cc66a049ec105048f0ab8dfbdf60bf4b", "shasum": ""}, "require": {"bower-asset/inputmask": "~3.2.2 | ~3.3.5", "bower-asset/jquery": "3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "1.3.*", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "~4.6", "lib-pcre": "*", "php": ">=5.4.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "http://www.yiiframework.com/", "keywords": ["framework", "yii2"], "time": "2020-09-14T21:52:10+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/94bb3f66e779e2774f8776d6e1bdeab402940510", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "time": "2020-06-24T00:04:01+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.1.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/0d8ce76b2dd036a5fc38b26434e1c672ad8975a9", "reference": "0d8ce76b2dd036a5fc38b26434e1c672ad8975a9", "shasum": ""}, "require": {"ext-mbstring": "*", "opis/closure": "^3.3", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "yii2"], "time": "2020-12-23T16:36:12+00:00"}, {"name": "yiisoft/yii2-queue", "version": "2.3.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-queue/zipball/a6758a2b9c2d8aca9b949f4faa9e3cca0dfbc8be", "reference": "a6758a2b9c2d8aca9b949f4faa9e3cca0dfbc8be", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/process": "^3.3||^4.0||^5.0", "yiisoft/yii2": "~2.0.14"}, "suggest": {"aws/aws-sdk-php": "Need for aws SQS.", "enqueue/amqp-lib": "Need for AMQP interop queue.", "enqueue/stomp": "Need for Stomp queue.", "ext-gearman": "Need for Gearman queue.", "ext-pcntl": "Need for process signals.", "pda/pheanstalk": "Need for Beanstalk queue.", "php-amqplib/php-amqplib": "Need for AMQP queue.", "yiisoft/yii2-redis": "Need for Redis queue."}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"yii\\queue\\": "src", "yii\\queue\\amqp\\": "src/drivers/amqp", "yii\\queue\\amqp_interop\\": "src/drivers/amqp_interop", "yii\\queue\\beanstalk\\": "src/drivers/beanstalk", "yii\\queue\\db\\": "src/drivers/db", "yii\\queue\\file\\": "src/drivers/file", "yii\\queue\\gearman\\": "src/drivers/gearman", "yii\\queue\\redis\\": "src/drivers/redis", "yii\\queue\\sync\\": "src/drivers/sync", "yii\\queue\\sqs\\": "src/drivers/sqs", "yii\\queue\\stomp\\": "src/drivers/stomp"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "z<PERSON>av<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Yii2 Queue Extension which supported DB, Redis, RabbitMQ, Beanstalk, SQS and Gearman", "keywords": ["async", "beanstalk", "db", "gearman", "gii", "queue", "rabbitmq", "redis", "sqs", "yii"], "time": "2020-12-23T17:04:23+00:00"}, {"name": "yiisoft/yii2-swiftmailer", "version": "2.1.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-swiftmailer/zipball/09659a55959f9e64b8178d842b64a9ffae42b994", "reference": "09659a55959f9e64b8178d842b64a9ffae42b994", "shasum": ""}, "require": {"swiftmailer/swiftmailer": "~6.0", "yiisoft/yii2": ">=2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"yii\\swiftmailer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The SwiftMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "swift", "swiftmailer", "yii2"], "time": "2018-09-23T22:00:47+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "platform-overrides": {"php": "7.0"}, "plugin-api-version": "1.1.0"}