# The environment Craft is currently running in ('dev', 'staging', 'production', etc.)
ENVIRONMENT="production"

# The secure key Craft will use for hashing and encrypting data
SECURITY_KEY="PuGG1b7FALEvgqm86hRjdLkLyZjgmc7a"

# The database driver that will be used ('mysql' or 'pgsql')
DB_DRIVER="mysql"

# The database server name or IP address (usually this is 'localhost' or '127.0.0.1')
DB_SERVER="localhost"

# The database username to connect with
DB_USER="christod_usr"

# The database password to connect with
DB_PASSWORD="1!2@3#"

# The name of the database to select
DB_DATABASE="christod_chb_new"

# The database schema that will be used (PostgreSQL only)
DB_SCHEMA="public"

# The prefix that should be added to generated table names (only necessary if multiple things are sharing the same database)
DB_TABLE_PREFIX=""

# The port to connect to the database with. Will default to 5432 for PostgreSQL and 3306 for MySQL.
DB_PORT="3306"

DEFAULT_SITE_URL="https://www.chb.gr/"
GREEK_SITE_URL="https://www.chb.gr/el"
SPANISH_SITE_URL="https://www.chb.gr/es"
