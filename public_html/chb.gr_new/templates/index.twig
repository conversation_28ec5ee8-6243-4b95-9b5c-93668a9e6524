{% extends "_layouts/site" %}

{% set title = 'Home' %}
{% set bodyClass = '' %}

{% block body %}

    {% set heroBanner = entry.heroBanner.all() %}
    {% if heroBanner|length %}
    <div class="ht-hero-section fix ht-hero-two">
        <div class="ht-hero-slider">
            {% for block in heroBanner %}
                {% set relFeaturedImage = block.heroImage.one() %}
            <div class="ht-single-slide" style="background-image: url({{ relFeaturedImage.getUrl() }})">
                <div class="ht-hero-content-one container">
                    <h3>CHB</h3>
                    <h1 class="">{{  block.heroTitle | nl2br }} </h1> {#cssanimation leDoorCloseLeft sequence#}
                    <p>{{  block.heroText }} </p>
                    <a href="{{  block.heroLink }}" class="default-btn large circle blue hover-blue uppercase">{{ 'Read more'|t }}</a>
                </div>
            </div>
            {% endfor %}
            {#  VIDEO AS 7TH BANNER#}
        </div>
    </div>
    {% endif %}

    {% set blocks = entry.boxes.all() %}
    {% if blocks|length %}
    <div class="banner-area home-boxes">
        <div class="container" style="width:100%; max-width:100%; padding:0 4px;">
            <div class="row">
                {% for block in blocks %}
                <div class="col-lg-3 col-md-6 col-sm-12">
                    <div class="banner-wrap home-box"
                         data-aos="fade-up"
                         data-aos-offset="40"
                         data-aos-delay="20"
                         data-aos-duration="700"
                         data-aos-easing="ease-in-out"
                         data-aos-mirror="true"
                         data-aos-once="true"
                         data-aos-anchor-placement="top-left"
                    >
                        {% set relFeaturedImage = block.boxImage.one() %}
                        <a href="{{ block.boxLink }}" class="banner-content__image">
                            <img src="{{ relFeaturedImage ? relFeaturedImage.getUrl() : alias('@assetBaseUrl/img/blog/1.jpg')}}" alt="banner">
                        </a>
                        <div class="banner-content">
                            <h4><a href="{{ block.boxLink }}" style="color:#000; font-size:24px;">{{ block.boxTitle }}</a></h4>
                            {% if block.boxText|length %}
                                <p>{{ block.boxText }}</p>
                            {% endif %}
                            <div class="banner-btn">
                                <a href="{{ block.boxLink }}">{{ 'Read more'|t }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    {% set imageGallery = entry.imageGalleryMl.all() %}
    {% if imageGallery|length %}
    <div class="blog-area pt-60 pb-60" style="background:#f6f5f1;">
        <div class="container">
            <div class="banner-text text-center pb-30">
                <h1>{{ 'CHB Group at a <span>glance</span>'|t|raw }}</h1>
            </div>
            <div class="info-boxes"
                 data-aos="fade-up"
                 data-aos-offset="10"
                 data-aos-delay="300"
                 data-aos-duration="700"
                 data-aos-easing="ease-in-out"
                 data-aos-mirror="true"
                 data-aos-once="true"
                 data-aos-anchor-placement="top-left">
                {% for rel in imageGallery %}
                <div class="info-box"><img src="{{ rel.url  }}" alt="">{#<div class="info-box__title">Title Here</div>#}</div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <div class="banner-area fix pt-70 pb-190 banner-two-area custom-featured-box">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-8">
                    <div class="banner-text pt-130">
                        <h3><span style="color:#0072c9;"> {{ entry.promoBannerSubtitle }}</span></h3>
                        <h1 style="font-size:28px;"> {{ entry.promoBannerTitle }}</h1>
                        <!--                            <h2>-->
                        <!--                                <img src="{{ siteUrl }}assets/img/icon/mark.png" alt="">-->
                        <!--                                <span>Buy 1 get 1 free</span>-->
                        <!--                            </h2>-->
                        {{ entry.promoBannerText }}
                        <br>
                        <a href="{{  entry.promoBannerLink }}" class="default-btn">{{ 'Read more'|t }}</a>
                    </div>
                </div>
            </div>
            <div class="custom-featured-image">
                {% set relFeaturedImage = entry.promoBannerImage.one() %}
                <img src="{{ relFeaturedImage.getUrl() }}" alt="">
            </div>
        </div>
    </div>

    <!-- Shop Banner Area Start -->
{#    <div class="shop-banner-area pb-60">#}
{#        <div class="container">#}
{#            <div class="row">#}
{#                <div class="col-md-6">#}
{#                    <div class="shop-banner-img">#}
{#                        <a href=""><img src="{{ siteUrl }}assets/_/img/ext/infographics-1.jpg" alt=""></a>#}
{#                    </div>#}
{#                </div>#}
{#                <div class="col-md-6">#}
{#                    <div class="shop-banner-img">#}
{#                        <a href=""><img src="{{ siteUrl }}assets/_/img/ext/infographics-2.jpg" alt=""></a>#}
{#                    </div>#}
{#                </div>#}
{#            </div>#}
{#        </div>#}
{#    </div>#}

{% endblock %}
