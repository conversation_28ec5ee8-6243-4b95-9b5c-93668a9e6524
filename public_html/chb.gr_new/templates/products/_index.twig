{% set pageParent = craft.entries.id(529).one() %}
{% set excludedIds = craft.entries.section('products').hiddenInLists(1).ids() %}
{% set excludedIdsString = excludedIds | join(', not ') %}
{% set allEntries = craft.entries.section('products').id('and, not '~excludedIdsString) %}

    {% set featuredImage = entry.featuredImageFull.one() %}

    <div class="breadcrumb-area text-center" style="background: url({% if featuredImage %} {{ featuredImage ? featuredImage.getUrl() : ''}} {% endif %}) top center no-repeat; background-size:contain;">
        <div class="container">

            <h1>{{  pageParent.title }}</h1>
            {% if pageParent.level > 0 %}
                <nav aria-label="breadcrumb">
                    <ul class="breadcrumb">
                        {% for crumb in pageParent.getAncestors() %}
                            <li class="breadcrumb-item">{{ crumb.getLink() }}</li>
                        {% endfor %}
                        <li class="breadcrumb-item active" aria-current="page">{{ pageParent.title }}</li>
                    </ul>
                </nav>
            {% endif %}
        </div>
    </div>

<div class="container" style="padding-top:60px;">
    {% if entry.body|length %}
        <div class="banner-area__excerpt2">
            {{ entry.body }}
        </div>
    {% endif %}
</div>

{% if allEntries|length %}
    <div class="blog-area pb-95 pt-60">
        <div class="container" style="">
            <div class="row-flex">
                {% for entry in allEntries %}
                    <div class="col-flex-4 text-center">
                        <div class="single-blog single-blog2"
                             data-aos="zoom-in-up"
                             data-aos-offset="30"
                             data-aos-delay="50"
                             data-aos-duration="700"
                             data-aos-easing="ease-in-out"
                             data-aos-mirror="true"
                             data-aos-once="true"
                             data-aos-anchor-placement="top-left"

                        >
                            {% if entry.alternativeLink|length %}
                                {% set url = entry.alternativeLink %}
                            {% else %}
                                {% set url = entry.url %}
                            {% endif %}
                            <div class="blog-image">
                                {% set entryFeaturedImage = entry.featuredImageList.one() %}
                                <a href="{{ url }}"><img src="{{ entryFeaturedImage ? entryFeaturedImage.getUrl('listFeaturedImage') : alias('@assetBaseUrl/img/blog/1.jpg')}}" alt=""></a>
                            </div>
                            <div class="blog-text" style="border: 1px solid #e8e8e8; ">
                                <h4><a href="{{ url }}">{{ entry.title }}</a></h4>
                                {% if entry.body|length %}
                                    <p>{{ entry.body }}</p>
                                {% endif %}
                                <a href="{{ url }}" class="default-btn">{{ 'Read more'|t }}</a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>


{% endif %}
