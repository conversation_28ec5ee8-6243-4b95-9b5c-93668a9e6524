{% extends "_layouts/site" %}

{% set title = entry.title %}
{% set bodyClass = '' %}
{#{%#}
{#    set seo = craft.seo.custom(#}
{#        entry.title,#}
{#        entry.body|wordLimit(20),#}
{#    )#}
{#%}#}

{% set pageParent = craft.entries.id(529).one() %}
{#{% set featuredImage = pageParent.featuredImageFull.one() %}#}
{% set featuredImage = entry.customHeroBanner.one() %}

{% block body %}

    <div class="breadcrumb-area text-center"  style="background: url({% if featuredImage %} {{ featuredImage ? featuredImage.getUrl() : ''}} {% endif %}) top center no-repeat; background-size:contain;">
        <div class="container">
            <h1>{{ entry.title }}</h1>
            <nav aria-label="breadcrumb">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ pageParent.url }}">{{ pageParent.title }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ entry.title }}</li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="blog-area pt-40">
        <div class="container">
            <div class="post-area">
                <div class="single-post-item custom-single-post-item" style="border:0; margin-bottom:0;">
                    <div class="single-post-info-text text-center" style="">
                        <div class="banner-area__excerpt2">{{ entry.body }}</div>
                        {% set featuredImage = entry.featuredImageList.one() %}
                        {% if featuredImage %}
                            <div class="single-post-img text-center">
                                <img src="{{ featuredImage.getUrl() }}" alt="">
                            </div>
                        {% endif %}
                        {% if entry.body2|length %}
                        <div class="banner-area__excerpt2">{{ entry.body2 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% set allEntries = entry.relatedProducts %}
    {% if allEntries|length %}
        <div class="blog-area pb-95 pt-90">
            <div class="container">
                <div class="section-title text-center mb-50">
                    <div class="section-img d-flex justify-content-center">
                        {#                        <img src="{{ siteUrl }}assets/img/icon/title.png" alt="">#}
                    </div>
                    <h2><span>{{ 'Products'|t }} </span> </h2>
                </div>
            </div>
            <div class="container" style="/*max-width:830px;*/">
                <div class="row-flex">
                    {% for rel in allEntries.all() %}
                        <div class="col-flex-2 text-center">
                            <div class="single-blog single-blog2"
                                 data-aos="zoom-in-up"
                                 data-aos-offset="30"
                                 data-aos-delay="50"
                                 data-aos-duration="700"
                                 data-aos-easing="ease-in-out"
                                 data-aos-mirror="true"
                                 data-aos-once="true"
                                 data-aos-anchor-placement="top-left"
                            >
                                <div class="blog-image">
                                    {% set relFeaturedImage = rel.featuredImageList.one() %}
                                    <img src="{{ relFeaturedImage ? relFeaturedImage.getUrl() : '//via.placeholder.com/170x200'}}" alt="">
                                </div>
                                <div class="blog-text" style="border: 1px solid #e8e8e8; padding:15px;">
                                    <h4 style="font-size: 14px; margin-bottom:0;">{{ rel.title}}</h4>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}
    
{% endblock %}
