{% extends "_layouts/site" %}

{% set title = entry.title %}
{% set bodyClass = '' %}

{% block body %}

    {% if entry.slug == 'fruit-portfolio' %}
        {% include "fruits/_index" %}
    {% elseif entry.slug == 'product-applications' %}
        {% include "aplications/_index" %}
    {% elseif entry.slug == 'product-portfolio' %}
        {% include "products/_index" %}
    {% elseif entry.slug == 'certifications' %}
        {% include "certifications/_index" %}
    {% else %}

    {% set featuredImage = entry.featuredImageFull.one() %}

    <div class="breadcrumb-area bg-12 text-center" style="background: url({% if featuredImage %} {{ featuredImage ? featuredImage.getUrl() : ''}} {% endif %}) top center no-repeat; background-size:contain;">
        <div class="container">
            <h1>{{ entry.title }}</h1>
            {% if entry.level > 0 %}
            <nav aria-label="breadcrumb">
                <ul class="breadcrumb">
                    {% for crumb in entry.getAncestors() %}
                        <li class="breadcrumb-item">{{ crumb.getLink() }}</li>
                    {% endfor %}
                    <li class="breadcrumb-item active" aria-current="page">{{ entry.title }}</li>
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>

    {% if entry.body|length %}
    <div class="blog-area pt-40">
        <div class="container">
            <div class="post-area">
                <div class="single-post-item" style="border-bottom: 0; margin-bottom: 0;">
                    <div class="single-post-info-text text-center">
                        <div class="banner-area__excerpt2">{{ entry.body }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <div class="post-area single-blog-area pt-60 pb-20">
        <div class="container">
            <div class="row">
                <div class="col-12"> {#col-xl-9 col-lg-8#}
                    <div class="single-post-item pb-60">
{#                        {% if featuredImage %}#}
{#                        <div class="single-post-img text-center">#}
{#                            <img src="{{ featuredImage ? featuredImage.getUrl('fullFeaturedImage') : ''}}" alt="" style=" max-width:100%; height:auto;">#}
{#                        </div>#}
{#                        {% endif %}#}
                        {% if entry.body2|length %}
                        <div class="banner-area__excerpt2 text-center">
                            {{ entry.body2 }}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {#<div class="col-xl-3 col-lg-4">
                    <div class="sidebar-wrapper">

                        <div class="sidebar-widget">
                            <h3>Recent Posts</h3>
                            <div class="sidebar-widget-option-wrapper">
                                <div class="sidebar-widget-option">
                                    <a href="blog-details.html">Blog Image Post</a>
                                </div>
                                <div class="sidebar-widget-option">
                                    <a href="blog-details.html">Post With Gallery</a>
                                </div>
                                <div class="sidebar-widget-option">
                                    <a href="blog-details.html">Post With Audio</a>
                                </div>
                                <div class="sidebar-widget-option">
                                    <a href="blog-details.html">Post With Video</a>
                                </div>
                                <div class="sidebar-widget-option">
                                    <a href="blog-details.html">Post Image Link</a>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>#}
            </div>
        </div>
    </div>


    {% set blocks = entry.boxes.all() %}
    {% if blocks|length %}
        <div class="blog-area pb-95 pt-0">
            <div class="container" style="">
                <div class="row-flex">
                    {% for block in blocks %}
                        <div class="col-flex-4 text-center">
                            <div class="single-blog single-blog2"
                                 data-aos="zoom-in-up"
                                 data-aos-offset="30"
                                 data-aos-delay="50"
                                 data-aos-duration="700"
                                 data-aos-easing="ease-in-out"
                                 data-aos-mirror="true"
                                 data-aos-once="true"
                                 data-aos-anchor-placement="top-left"
                            >
                                <div class="blog-image">
                                    {% set relFeaturedImage = block.boxImage.one() %}
                                    <img src="{{ relFeaturedImage ? relFeaturedImage.getUrl('listFeaturedImage') : alias('@assetBaseUrl/img/blog/1.jpg')}}" alt=""  style=" max-width:100%; height:auto;">
                                </div>
                                <div class="blog-text" style="border: 1px solid #e8e8e8; ">
                                    <h4>{{ block.boxTitle }}</h4>
                                    {% if block.boxText|length %}
                                        <p>{{ block.boxText }}</p>
                                    {% endif %}
                                    {% if block.boxLink|length %}
                                        <a href="{{ block.boxLink }}" class="default-btn">{{ 'Read more'|t }}</a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    {% endif %}

    {% if entry.slug == 'history' %}
        {% set blocks = timeline.timeline.all() %}
        {% if blocks|length %}
            <div class="timeline">
                {% for block in blocks %}
                    <div class="timeline__item">
                        <div class="timeline__item__title big">{{ block.timelineYear }}</div>
                        <div class="timeline__item__body"
                             data-aos="fade-up"
                             data-aos-offset="10"
                             data-aos-delay="300"
                             data-aos-duration="700"
                             data-aos-easing="ease-in-out"
                             data-aos-mirror="true"
                             data-aos-once="true"
                             data-aos-anchor-placement="top-left"
                        >
                            {{ block.timelineText }}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endif %}

{% endblock %}
