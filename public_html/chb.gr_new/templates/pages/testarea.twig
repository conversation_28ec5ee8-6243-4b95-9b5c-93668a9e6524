{% extends "_layouts/site" %}

{% set title = entry.title %}
{% set bodyClass = '' %}

{% block body %}

    <div class="blog-area pt-190">
        <div class="container">

           <h1>TEST AREA</h1>


            {% set blocks = timeline.timeline.all() %}
            {% if blocks|length %}
            <div class="timeline">
                {% for block in blocks %}
                    <div class="timeline__item">
                        <div class="timeline__item__title big">{{ block.timelineYear }}</div>
                        <div class="timeline__item__body"
                             data-aos="fade-up"
                             data-aos-offset="10"
                             data-aos-delay="300"
                             data-aos-duration="700"
                             data-aos-easing="ease-in-out"
                             data-aos-mirror="true"
                             data-aos-once="true"
                             data-aos-anchor-placement="top-left"
                        >
                            {{ block.timelineText }}
                        </div>
                    </div>
                {% endfor %}
            </div>
            {% endif %}


        </div>
    </div>


{% endblock %}
