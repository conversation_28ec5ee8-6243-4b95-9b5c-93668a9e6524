{% extends "_layouts/site" %}

{% set title = entry.title %}
{% set bodyClass = '' %}

{% block body %}

    <div class="contact-area fix mb-95 pt-190">
        <div class="contact-form pt-110">
            <h1 class="contact-title">{{ 'CONTACT US'|t }}</h1>
            <div id="thanks" style="color:green; text-align:center; font-size:18px; margin-bottom:20px; display:none;">{{ 'Your message was sent successfully. Thank you.'|t }}</div>

            <form id="contact-form" method="post" action="" accept-charset="UTF-8">
                <div class="form-messege">
                    {% macro errorList(errors) %}
                        {% if errors %}
                            <ul class="errors">
                                {% for error in errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    {% endmacro %}

                    {% from _self import errorList %}
                </div>
                {{ csrfInput() }}
                <input type="hidden" name="action" value="contact-form/send">

                <div class="row">
                    <div class="col-md-6">
                        <input id="from-name" type="text" name="fromName" value=""  placeholder="{{ 'First Name'|t }} *" required>
                        {{ message is defined and message ? errorList(message.getErrors('fromName')) }}
                    </div>
                    <div class="col-md-6">
                        <input id="from-last-name" type="text" name="fromLastName" value=""  placeholder="{{ 'Last Name'|t }} *" required>

                    </div>
                    <div class="col-md-6">
                        <input id="from-company" type="text" name="message[companyName]" value=""  placeholder="{{ 'Company Name'|t }} *" required>
                    </div>
                    <div class="col-md-6">
                        <input id="from-email" type="email" name="fromEmail" value="" placeholder="Email *" required>
                        {{ message is defined and message ? errorList(message.getErrors('fromEmail')) }}
                    </div>
                    <div class="col-md-12">
                        <input id="from-country" type="text" name="message[country]" value=""  placeholder="{{ 'Country'|t }} *" required>
                    </div>
                    <div class="col-md-6">
                        <select name="message[purpose]" class="custom-select">
                            <option value="——" selected="selected">{{ 'Purpose of contact'|t }}</option>
                            <option value="Information">{{ 'Information'|t }}</option>
                            <option value="Order">{{ 'Order'|t }}</option>
                            <option value="Cooperation">{{ 'Cooperation'|t }}</option>
                            <option value="Report an incident or bribery">{{ 'Report an incident or bribery'|t }}</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <select name="message[interest]" class="custom-select">
                            <option value="——" selected="selected">{{ 'I’m interested in'|t }}</option>
                            <option value="Canned fruits">{{ 'Canned fruits'|t }}</option>
                            <option value="Juice Blends">{{ 'Juice Blends'|t }}</option>
                            <option value="Compounds">{{ 'Compounds'|t }}</option>
                            <option value="Fruit Preparations">{{ 'Fruit Preparations'|t }}</option>
                            <option value="B2B products (juice concentrates, purees, dices, essential oils)">{{ 'B2B products (juice concentrates, purees, dices, essential oils)'|t }}</option>
                            <option value="Chris Family Juices">{{ 'Chris Family Juices'|t }}</option>
                            <option value="Othe">{{ 'Other'|t }}</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <select name="message[industry]" class="custom-select">
                            <option value="——" selected="selected">{{ 'My industry'|t }}</option>
                            <option value="Soft drinks">{{ 'Soft drinks'|t }}</option>
                            <option value="Fruit juices">{{ 'Fruit juices'|t }}</option>
                            <option value="Dairy & dairy alternatives">{{ 'Dairy & dairy alternatives'|t }}</option>
                            <option value="Bakery">{{ 'Bakery'|t }}</option>
                            <option value="Citrus flavors">{{ 'Citrus flavors'|t }}</option>
                            <option value="Ho.Re.Ca.">{{ 'Ho.Re.Ca.'|t }}</option>
                            <option value="Retail">{{ 'Retail'|t }}</option>
                            <option value="Jam preparation">{{ 'Jam preparation'|t }}</option>
                            <option value="Cider">{{ 'Cider'|t }}</option>
                            <option value="Other">{{ 'Other'|t }}</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <input id="subject" type="text" name="subject" value=""  placeholder="{{ 'Subject'|t }} *">
                        {{ message is defined and message ? errorList(message.getErrors('subject')) }}
                    </div>
                </div>
                <textarea id="message" name="message[body]" placeholder="{{ 'Message'|t }} *"></textarea>
                {{ message is defined and message.body ? errorList(message.getErrors('message.body')) }}
                <button type="submit" class="submit-btn default-btn">
                    <span>{{ 'Send Email'|t }}</span>
                </button>
            </form>

        </div>
        <div class="contact-address pt-110 pb-100">
            <h1 class="contact-title">{{ 'INFO'|t }}</h1>
            <div class="contact-info">
                {{ entry.body }}
                {% if entry.contactInfo|length %}
                <div class="contact-list-wrapper">
                    {% for row in entry.contactInfo %}
                        {% if row.contactInfoType == 'phone' %}
                            <div class="contact-list">
                                <i class="fa fa-phone"></i>
                                <span>{{ row.contactInfoText }}</span>
                            </div>
                        {% endif %}
                        {% if row.contactInfoType == 'fax' %}
                            <div class="contact-list">
                                <i class="fa fa-fax"></i>
                                <span>{{ row.contactInfoText }}</span>
                            </div>
                        {% endif %}
                        {% if row.contactInfoType == 'email' %}
                            <div class="contact-list">
                                <i class="fa fa-envelope-o"></i>
                                <span><a href="mailto:{{ row.contactInfoText }}">{{ row.contactInfoText }}</a></span>
                            </div>
                        {% endif %}
                        {% if row.contactInfoType == 'address' %}
                            <div class="contact-list">
                                <i class="fa fa-home"></i>
                                <span>{{ row.contactInfoText }}</span>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>


{% endblock %}
