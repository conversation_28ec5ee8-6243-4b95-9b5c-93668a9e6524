{% extends "_layouts/site" %}

{% set title = entry.title %}
{% set bodyClass = '' %}
{#{%#}
{#    set seo = craft.seo.custom(#}
{#        entry.title,#}
{#        entry.body|wordLimit(20),#}
{#    )#}
{#%}#}

{% block body %}

    {% set featuredImage = entry.featuredImageFull.one() %}
    <div class="banner-area fix banner-two-area bg-1 pt-95">
        <div class="pt-70 pb-190"  {#style="background: rgba(0, 0, 0, 0) url({{ featuredImage ? featuredImage.getUrl('fullFeaturedImage') : ''}}) no-repeat scroll left center; background-size:contain;"#}>

            <div class="featuredImage"
                 style="background: rgba(0, 0, 0, 0) url({{ featuredImage ? featuredImage.getUrl('fullFeaturedImage') : ''}}) no-repeat scroll left center; background-size:contain;"
                 data-aos="fade-right"
                 data-aos-offset="200"
                 data-aos-delay="50"
                 data-aos-duration="1000"
                 data-aos-easing="ease-in-out"
                 data-aos-mirror="true"
                 data-aos-once="false"
                 data-aos-anchor-placement="top-left"
            >
            </div>

            <div class="container" style="max-width:900px;"
                 data-aos="fade-top"
                 data-aos-offset="200"
                 data-aos-delay="50"
                 data-aos-duration="1000"
                 data-aos-easing="ease-in-out"
                 data-aos-mirror="true"
                 data-aos-once="false"
                 data-aos-anchor-placement="top-left"
            >
                <div class="row" style="justify-content: end">
                    <div class="col-md-8">
                        <div class="banner-text pt-130">
                            <div class="heading">
{#                                <span class="UpBorder" style="width: 140px;"></span>#}
                                <h2 class="h1">
                                    {{ entry.title }}
                                    <div class="iconLeaf"></div>
                                </h2>
{#                                <span class="DownBorder" style="width: 140px;"></span>#}
                            </div>
                            {% if entry.body|length %}
                            <div class="banner-area__excerpt">{{ entry.body }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if entry.body2|length %}
    <div class="blog-area  pt-60">
        <div class="container">
            <div class="post-area">
                <div class="single-post-item" style="border-bottom: 0; margin-bottom: 0;">
                    <div class="single-post-info-text text-left">
                        <div class="relativeBorder text-center scale" style="">
                            <span class="UpBorder"></span>
                            <h2 class="fadingTop h1">{{ entry.title }} Portfolio</h2>
                            <span class="DownBorder"></span>
                        </div>
                        <div class="banner-area__excerpt2">{{ entry.body2 }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% set allEntries = entry.relatedProducts.all() %}
    {% if allEntries|length %}
        <div class="blog-area pb-95 pt-90">
            <div class="container">
                <div class="section-title text-center mb-50">
                    <div class="section-img d-flex justify-content-center">
                        {#                        <img src="{{ siteUrl }}assets/img/icon/title.png" alt="">#}
                    </div>
                    <h2><span>{{ 'Products'|t }} </span> </h2>
                </div>
            </div>
            <div class="container" style="/*max-width:830px;*/">
                <div class="row-flex">
                    {% for rel in allEntries %}
                        <div class="col-flex-2 text-center">
                            <div class="single-blog single-blog2"
                                 data-aos="zoom-in-up"
                                 data-aos-offset="30"
                                 data-aos-delay="50"
                                 data-aos-duration="700"
                                 data-aos-easing="ease-in-out"
                                 data-aos-mirror="true"
                                 data-aos-once="true"
                                 data-aos-anchor-placement="top-left"
                            >
                                <div class="blog-image">
                                    {% set relFeaturedImage = rel.featuredImageList.one() %}
                                    <img src="{{ relFeaturedImage ? relFeaturedImage.getUrl() : '//via.placeholder.com/170x200'}}" alt="">
                                </div>
                                <div class="blog-text" style="border: 1px solid #e8e8e8; padding:15px;">
                                    <h4 style="font-size: 14px; margin-bottom:0;">{{ rel.title}}</h4>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    {% set allEntries = craft.entries.section('fruits').all() %}
    {% if allEntries|length %}
        <div class="blog-area pb-95 pt-90">
            <div class="container">
                <div class="section-title text-center mb-50">
                    <div class="section-img d-flex justify-content-center">
{#                        <img src="{{ siteUrl }}assets/img/icon/title.png" alt="">#}
                    </div>
                    <h2><span>{{ 'Fruits'|t }}  </span>& {{ 'Portfolio'|t }}</h2>
                </div>
            </div>
            <div class="container">
                <div class="row-flex">
                    {% for rel in allEntries %}
                        <div class="col-flex-4 text-center">
                            <div class="single-blog single-blog2"
                                data-aos="zoom-in-up"
                                data-aos-offset="30"
                                data-aos-delay="50"
                                data-aos-duration="700"
                                data-aos-easing="ease-in-out"
                                data-aos-mirror="true"
                                data-aos-once="true"
                                data-aos-anchor-placement="top-left"
                                 style="border:0"

                            >
                                <div class="blog-image">
                                    {% set relFeaturedImage = rel.featuredImageFull.one() %}
                                    <a href="{{ rel.url }}" style="max-width: 300px;margin: 0 auto;"><img src="{{ relFeaturedImage ? relFeaturedImage.getUrl('fullFeaturedImage') : alias('@assetBaseUrl/img/blog/1.jpg') }}" alt=""></a>
                                </div>
                                <div class="blog-text" style="border: 1px solid #e8e8e8; ">
                                    <h4><a href="{{ rel.url }}">{{ rel.title }}</a></h4>
                                    {% if rel.body|length %}
                                        <p>{{ rel.body }}</p>
                                    {% endif %}
                                    <a href="{{ rel.url }}" class="default-btn">{{ 'Read more'|t }}</a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

{% endblock %}
