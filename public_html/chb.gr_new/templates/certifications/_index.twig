{% set plants = craft.entries.section('plants').all() %}
{% set featuredImage = entry.featuredImageFull.one() %}
<div class="breadcrumb-area bg-12 text-center" style="background: url({% if featuredImage %} {{ featuredImage ? featuredImage.getUrl() : ''}} {% endif %}) top center no-repeat; background-size:contain;">
    <div class="container">
        <h1>{{ entry.title }}</h1>
        {% if entry.level > 0 %}
            <nav aria-label="breadcrumb">
                <ul class="breadcrumb">
                    {% for crumb in entry.getAncestors() %}
                        <li class="breadcrumb-item">{{ crumb.getLink() }}</li>
                    {% endfor %}
                    <li class="breadcrumb-item active" aria-current="page">{{ entry.title }}</li>
                </ul>
            </nav>
        {% endif %}
    </div>
</div>

<div class="container" style="padding-top:60px;">
    {% if entry.body|length %}
        <div class="banner-area__excerpt2">
            {{ entry.body }}
        </div>
    {% endif %}

    {% if plants|length %}
        <div class="custom-accordion">
            <ul>
                {% for plant in plants %}
                    {% set certs = craft.entries.section('certificates').relatedTo(plant).all() %}
                    <li>
                        <input type="checkbox" checked>
                        <i></i>
                        <h2>{{ plant.title }}</h2>
                        <div class="accordionContent">
                            {% for cert in certs %}
                                <h6>{{ cert.title }}</h6>
                            {% set docs = cert.documents.all() %}
                            <ul>
                                {% for doc in docs %}
                                    <li><a href="{{ doc.url }}">{{ doc.title }}</a></li>
                                {% endfor %}
                            </ul>
                            {% endfor %}
                        </div>

                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}

</div>

