<!doctype html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js"> <!--<![endif]-->
    <head>

        <!--<title>{% if title is defined %}{{ title }} | {% endif %}{{ siteName }}</title>-->
        {% hook "seo" %}

        <!--Meta-->
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

        <meta name="theme-color" content="#ad172b">


        {% if craft.app.config.env == "production" %}
            <!-- Google Tag Manager -->
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','GTM-5DT349N');</script>
            <!-- End Google Tag Manager -->


            <!-- Global site tag (gtag.js) - Google Analytics -->
            <script async src="https://www.googletagmanager.com/gtag/js?id=***********-1"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());

                gtag('config', '***********-1');
            </script>
        {% else %}
            <script>
                // Fake Google Analytics for working locally
                var gtag = gtag || (function(){});
            </script>
        {% endif %}


        <!--Favicons-->
{#        <link rel="apple-touch-icon-precomposed" sizes="57x57" href="{{ siteUrl }}_/images/favicons/apple-touch-icon-57x57.png" />#}
{#        <link rel="apple-touch-icon-precomposed" sizes="114x114" href="{{ siteUrl }}_/images/favicons/apple-touch-icon-114x114.png" />#}
{#        <link rel="apple-touch-icon-precomposed" sizes="72x72" href="{{ siteUrl }}_/images/favicons/apple-touch-icon-72x72.png" />#}
{#        <link rel="apple-touch-icon-precomposed" sizes="144x144" href="{{ siteUrl }}_/images/favicons/apple-touch-icon-144x144.png" />#}
{#        <link rel="apple-touch-icon-precomposed" sizes="120x120" href="{{ siteUrl }}_/images/favicons/apple-touch-icon-120x120.png" />#}
{#        <link rel="apple-touch-icon-precomposed" sizes="152x152" href="{{ siteUrl }}_/images/favicons/apple-touch-icon-152x152.png" />#}
{#        <link rel="icon" type="image/png" href="{{ siteUrl }}_/images/favicons/favicon-32x32.png" sizes="32x32" />#}
{#        <link rel="icon" type="image/png" href="{{ siteUrl }}_/images/favicons/favicon-16x16.png" sizes="16x16" />#}
{#        <meta name="application-name" content="St Basils"/>#}
{#        <meta name="msapplication-TileColor" content="#FFFFFF" />#}
{#        <meta name="msapplication-TileImage" content="{{ siteUrl }}_/images/favicons/mstile-144x144.png" />#}

        <link href="https://fonts.googleapis.com/css?family=Raleway:300,300i,400,400i,500,500i,600,600i&display=swap" rel="stylesheet">

        <!-- All css here -->
        <link rel="stylesheet" href="{{ alias('@assetBaseUrl') }}/css/bootstrap.min.css">
        <link rel="stylesheet" href="{{ alias('@assetBaseUrl') }}/css/font-awesome.min.css">
        <link rel="stylesheet" href="{{ alias('@assetBaseUrl') }}/css/ie7.css">
        <link rel="stylesheet" href="{{ alias('@assetBaseUrl') }}/css/plugins.css">
        <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
        <link rel="stylesheet" href="{{ alias('@assetBaseUrl') }}/css/style.css">
        <script src="{{ alias('@assetBaseUrl') }}/js/vendor/modernizr-3.5.0.min.js"></script>


        {% block head %}{% endblock %}

        <script>
            var jsVars = {
                baseUrl: "{{ siteUrl|trim('/') }}"
            }
        </script>


    </head>
	<body class="{% if bodyClass is defined %}{{ bodyClass }}{% endif %}" data-env="{{craft.app.config.env}}">

        {% if craft.app.config.env == "production" %}
            <!-- Google Tag Manager (noscript) -->
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5DT349N"
                              height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
            <!-- End Google Tag Manager (noscript) -->

        {% endif %}
        <!--========================================================-->

        {% block header %}{% endblock %}
        {% block body %}{% endblock %}
        {% block footer %}{% endblock %}

        <!--========================================================-->

        <script type="text/javascript">
            window.csrfTokenName = "{{ craft.app.config.general.csrfTokenName|e('js') }}";
            window.csrfTokenValue = "{{ craft.app.request.csrfToken|e('js') }}";
        </script>

        <script src="{{ alias('@assetBaseUrl') }}/js/vendor/jquery-3.2.1.min.js"></script>
        <script src="{{ alias('@assetBaseUrl') }}/js/popper.min.js"></script>
        <script src="{{ alias('@assetBaseUrl') }}/js/bootstrap.min.js"></script>
        <script src="{{ alias('@assetBaseUrl') }}/js/plugins.js"></script>
        {#<script src="{{ alias('@assetBaseUrl') }}/js/ajax-mail.js"></script>#}
        <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
        <script src="{{ alias('@assetBaseUrl') }}/js/main.js"></script>

        <!--========================================================-->

        {% block scripts %} {% endblock %}

        {% if craft.app.config.env == "production" %}


        {% else %}
        {# You are  in devmode #}
        {% endif %}
	</body>
</html>
