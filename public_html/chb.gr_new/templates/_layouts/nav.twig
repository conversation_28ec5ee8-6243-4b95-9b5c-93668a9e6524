<nav class="d-flex justify-content-center customNav">
    <ul class="subnav">
{#        <li><a href="">News & Media</a></li>#}
        {# Careers #}
        {% set page = craft.entries.id(1316).one() %}
        <li><a href="{{ page.url }}">{{ page.title }}</a></li>
        {% set contactPage = craft.entries.id(2255).one() %}
        <li><a href="{{ contactPage.url }}">{{ contactPage.title }}</a></li>
    </ul>
    <ul class="mainNav">
{#        <li><a href="index.html">Home</a></li>#}
        <li>
            {% set pageParent = craft.entries.id(257).one() %}
            {% set pageChildren = craft.entries.section('pages')
                .descendantOf(pageParent )
                .descendantDist(1)
                .all() %}
            <a>{{ pageParent.title }} <i class="fa fa-angle-down"></i></a>
            <ul>
                {% nav entry in pageChildren %}
                    <li>
                        <a href="{{ entry.url }}">{{ entry.title }}</a>
                        {% ifchildren %}
                            <ul>
                                {% children %}
                            </ul>
                        {% endifchildren %}
                    </li>
                {% endnav %}
            </ul>
        </li>
        {% set page = craft.entries.id(324).one() %}
        <li><a href="{{ page.url }}">{{ page.title }}</a></li>
        <li>
            <a href="{{ siteUrl }}markets-we-serve">{{ 'Markets we serve'|t }} <i class="fa fa-angle-down"></i></a>
            {% set markets = craft.entries.section('markets').all() %}
            <ul>
                {% if markets|length %}
                    {% for entry in markets %}
                        <li><a href="{{entry.url}}">{{entry.title}}</a></li>
                    {% endfor %}
                {% endif %}
            </ul>
        </li>
        <li>
            {% set pageParent = craft.entries.id(338).one() %}
            {% set pageChildren = craft.entries.section('pages')
                .descendantOf(pageParent )
                .descendantDist(1)
                .all() %}
            <a>{{ pageParent.title }} <i class="fa fa-angle-down"></i></a>
            <ul>
                {% nav entry in pageChildren %}
                    {% if entry.slug == 'product-portfolio' %}

                        {% set excludedIds = craft.entries.section('products').hiddenInLists(1).ids() %}
                        {% set excludedIdsString = excludedIds | join(', not ') %}
                        {% set allEntries = craft.entries.section('products').id('and, not '~excludedIdsString) %}
                        <li class="hasMenu">
                            <a href="{{ entry.url }}">{{ entry.title }}</a>
                            {% if allEntries|length %}
                            <ul>
                                {% for rel in allEntries %}
                                    <li><a href="{{ rel.url }}">{{ rel.title }}</a></li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </li>
                    {% elseif entry.slug == 'product-applications' %}
                        {% set allEntries = craft.entries.section('aplications').all() %}
                        <li class="hasMenu">
                            <a href="{{ entry.url }}">{{ entry.title }}</a>
                            {% if allEntries|length %}
                                <ul>
                                    {% for rel in allEntries %}
                                        {% set currentTarget = craft.entries.section('markets').relatedTo(rel).one() %}
                                        {% if currentTarget|length %}
                                            {% set url = currentTarget.url %}
                                        {% else %}
                                            {% set url = rel.url %}
                                        {% endif %}
                                        <li><a href="{{ url  }}">{{ rel.title }}</a></li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </li>
                    {% else %}
                    <li>
                        <a href="{{ entry.url }}">{{ entry.title }}</a>
                    </li>
                    {% endif %}
                {% endnav %}
            </ul>
        </li>
        <li>
            <a href=""><i class="fa fa-globe"></i>  <i class="fa fa-angle-down"></i></a>
            <ul style="width:50px; background: rgba(255,255,255,0.7);">
{#                <li style="">#}
{#                    <a href="">#}
{#                        <img src="https://www.chb.gr/wp-content/plugins/sitepress-multilingual-cms/res/flags/zh.png" alt="zh-hans" width="18" height="12"></a>#}
{#                </li>#}
                {% set element = (category ?? entry ?? null) %}
                {% for site in craft.app.sites.getAllSites() %}
                    {% set siteLink = siteSwitcher(site.handle, element) %}
                    {% if siteLink %}
                        <li><a href="{{ siteLink }}">{{ site.language|slice(0, 2)|lower }}</a></li>
                    {% endif %}
                {% endfor %}
            </ul>
        </li>
    </ul>
</nav>