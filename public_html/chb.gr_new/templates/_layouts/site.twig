{% extends "_layouts/base" %}

{% if bodyClass is not defined %}
	{% set bodyClass = entry is defined ? entry.section.handle : craft.app.request.segments|first  %}
{% endif %}

{# =================================== #}
{% block head %}

{% endblock %}
{# =================================== #}
{% block header %}

    <!-- Header Area Start -->
    <header class="header-area header-two header-sticky">
        <div class="header-container">
            <div class="row">
                <div class="col-lg-2 col-sm-4">
                    <div class="logo text-center">
                        <a href="{{ siteUrl|trim('/') }}"><img src="{{ alias('@assetBaseUrl') }}/_/img/logo.png" alt="CHB"></a>
                    </div>
                </div>
                <div class="col-lg-8 display-none-md display-none-xs">
                    <div class="ht-main-menu">
                        {% include "_layouts/nav" %}
                    </div>
                </div>
                {#
                <div class="col-lg-2 col-sm-8">
                    <div class="header-content d-flex justify-content-end">
                        <div class="search-wrapper">
                            <a href="#"><span class="icon icon-Search"></span></a>
                            <form action="#" class="search-form">
                                <input type="text" placeholder="{{ 'Search entire store here ...'|t }}">
                                <button type="button">{{ 'Search'|t }}</button>
                            </form>
                        </div>
                    </div>
                </div>
                #}
            </div>
        </div>
        <!-- Header Area End -->
        <!-- Mobile Menu Area Start -->
        <div class="mobile-menu-area">
            <div class="mobile-menu container">
                {% include "_layouts/navMobile" %}
            </div>
        </div>
        <!-- Mobile Menu Area End -->
    </header>
    <!-- Header Area End -->


{% endblock %}
{# =================================== #}
{% block body %}

{% endblock %}
{# =================================== #}
{% block footer %}

    <!-- Footer Area Start -->
    <footer class="footer-area">
        <!-- Footer Top Area Start -->
        <div class="footer-top bg-4 pt-120 pb-120">
            <!-- Newsletter Area Start -->
            <div class="newsletter-area">
                <div class="container text-center">
                    <div class="newsletter-container">
                        <h2>{{ 'Subscribe to our newsletter'|t }}</h2>
                        <p>{{ 'Signup for our newsletter to get the latest news and updates delivered directly to your inbox.'|t }}</p>
                        <!-- Begin Mailchimp Signup Form -->
                        <div id="mc_embed_signup" class="newsletter-form mc_embed_signup">
                            <form action="https://christodouloufamily.us10.list-manage.com/subscribe/post?u=fcb99f181afa4ffe99380792e&amp;id=bbdddbcc65" method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form" class="validate" target="_blank" novalidate>
                                <div id="mc_embed_signup_scroll" class="mc-form">
                                    <input type="email" value="" name="EMAIL" class="email" id="mce-EMAIL" placeholder="{{ 'Enter you email address here'|t }}..." required>
                                    <!-- real people should not fill this in and expect good things - do not remove this or risk form bot signups-->
                                    <div style="position: absolute; left: -5000px;" aria-hidden="true"><input type="text" name="b_fcb99f181afa4ffe99380792e_bbdddbcc65" tabindex="-1" value=""></div>
                                    <button type="submit" name="subscribe" id="mc-embedded-subscribe" class="default-btn">{{ 'Subscribe'|t }}</button>
                                </div>
                            </form>
                        </div>
                        <!--End mc_embed_signup-->
                    </div>
                    <div class="social-icon">
                       {# <a href="#"><i class="fa fa-twitter"></i></a>
                        <a href="#"><i class="fa fa-google-plus"></i></a>
                        <a href="#"><i class="fa fa-facebook"></i></a>
                        <a href="#"><i class="fa fa-youtube"></i></a>
                        <a href="#"><i class="fa fa-flickr"></i></a>
                        <a href="https://www.linkedin.com/company/chb-group/" target="_blank" class="active"><i class="fa fa-linkedin"></i></a>#}
                        <a href="https://www.linkedin.com/company/chb-group/" target="_blank" class="active"><img src="{{ alias('@assetBaseUrl') }}/_/img/linkedin.png" alt=""></a>
                    </div>
                </div>
            </div>

            <!-- Footer Widget Area Start -->
            <div class="footer-widget-area">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-4 col-md-6">
                            <div class="single-footer-widget" style="padding-top:0;">
                                <div class="footer-logo">
                                    <a href="#"><img src="{{ alias('@assetBaseUrl') }}/_/img/logo.png" alt="" width="50"></a>
                                </div>
                                {% set contactPage = craft.entries.id(2255).one() %}
                                {{ contactPage.body }}
                                {% if contactPage.contactInfo|length %}
                                <div class="footer-text">
                                    {% for row in contactPage.contactInfo %}
                                        {% if row.contactInfoType == 'phone' %}
                                            <span><i class="icon icon-Phone"></i>{{ 'Phone'|t }} : {{ row.contactInfoText }}</span>
                                        {% endif %}
                                        {% if row.contactInfoType == 'fax' %}
                                            <span><i class="icon icon-Phone"></i>Fax : {{ row.contactInfoText }}</span>
                                        {% endif %}
                                        {% if row.contactInfoType == 'email' %}
                                            <span><i class="icon icon-Mail"></i>Email : <a href="mailto:{{ row.contactInfoText }}">{{ row.contactInfoText }}</a></span>
                                        {% endif %}
                                        {% if row.contactInfoType == 'address' %}
                                            <span><i class="icon icon-Pointer"></i>{{ 'Address'|t }} :  {{ row.contactInfoText }} </span>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        {% set navEntries = craft.entries.section('markets').all() %}
                        {% if navEntries|length %}
                        {% set page = craft.entries.id(37).one() %}
                        <div class="col-lg-4 col-md-3">
                            <div class="single-footer-widget" style="padding-top:0;">
                                <h3><a href="{{ page.url }}">{{ page.title }}</a></h3>
                                <ul class="footer-widget-list">
                                    {% for entry in navEntries %}
                                        <li><a href="{{entry.url}}">{{entry.title}}</a></li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        {% endif %}


                        {% set excludedIds = craft.entries.section('products').hiddenInLists(1).ids() %}
                        {% set excludedIdsString = excludedIds | join(', not ') %}
                        {% set navEntries = craft.entries.section('products').id('and, not '~excludedIdsString) %}
                        {% if navEntries|length %}
                            {% set page = craft.entries.id(529).one() %}
                            <div class="col-lg-4 col-md-3">
                                <div class="single-footer-widget" style="padding-top:0;">
                                    <h3><a href="{{ page.url }}">{{ page.title }}</a></h3>
                                    <ul class="footer-widget-list">
                                        {% for entry in navEntries %}
                                            <li><a href="{{entry.url}}">{{entry.title}}</a></li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        {% endif %}

                        {#{% set navEntries = craft.entries.section('aplications').all() %}
                        {% if navEntries|length %}
                            {% set page = craft.entries.id(526).one() %}
                            <div class="col-lg-2 col-md-3">
                                <div class="single-footer-widget" style="padding-top:0;">
                                    <h3><a href="{{ page.url }}">{{ page.title }}</a></h3>
                                    <ul class="footer-widget-list">
                                        {% for entry in navEntries %}
                                            <li><a href="{{entry.url}}">{{entry.title}}</a></li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        {% endif %}#}

                    </div>
                </div>
            </div>
            <!-- Footer Widget Area End -->
        </div>
        <!-- Footer Top Area End -->
        <!-- Footer Bottom Area Start -->
        <div class="footer-bottom-area pt-15 pb-30">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6 d-flex col-md-6">
                        <div class="footer-text-bottom">
                            <p>Copyright &copy; {{ now.year }} <a href="#">chb.gr</a>. All Rights Reserved</p>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="payment-img d-flex justify-content-end">
                            developed by&nbsp;<a href="http://www.pgworks.gr" target="_blank">pgworks</a>&nbsp;|&nbsp;designed by&nbsp;<a href="http://eptacreative.gr/en/" target="_blank">Epta Creative</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer Bottom Area End -->
    </footer>
    <!-- Footer Area End -->


{% endblock %}
{# =================================== #}
{% block scripts %}

    {% block pagescripts %} {% endblock %}

{% endblock %}
{# =================================== #}