<nav id="mobile-menu-active">
    <ul class="menu-overflow">
        {#        <li><a href="index.html">Home</a></li>#}
        <li>
            {% set pageParent = craft.entries.id(257).one() %}
            {% set pageChildren = craft.entries.section('pages')
                .descendantOf(pageParent )
                .descendantDist(1)
                .all() %}
            <a>{{ pageParent.title }} <i class="fa fa-angle-down"></i></a>
            <ul>
                {% nav entry in pageChildren %}
                    <li>
                        <a href="{{ entry.url }}">{{ entry.title }}</a>
                        {% ifchildren %}
                            <ul>
                                {% children %}
                            </ul>
                        {% endifchildren %}
                    </li>
                {% endnav %}
            </ul>
        </li>
        {% set page = craft.entries.id(324).one() %}
        <li><a href="{{ page.url }}">{{ page.title }}</a></li>
        <li>
            <a href="{{ siteUrl }}markets-we-serve">{{ 'Markets we serve'|t }} <i class="fa fa-angle-down"></i></a>
            {% set markets = craft.entries.section('markets').all() %}
            <ul>
                {% if markets|length %}
                    {% for entry in markets %}
                        <li><a href="{{entry.url}}">{{entry.title}}</a></li>
                    {% endfor %}
                {% endif %}
            </ul>
        </li>
        <li>
            {% set pageParent = craft.entries.id(338).one() %}
            {% set pageChildren = craft.entries.section('pages')
                .descendantOf(pageParent )
                .descendantDist(1)
                .all() %}
            <a>{{ pageParent.title }} <i class="fa fa-angle-down"></i></a>
            <ul>
                {% nav entry in pageChildren %}
                    {% if entry.slug == 'product-portfolio' %}

                        {% set excludedIds = craft.entries.section('products').hiddenInLists(1).ids() %}
                        {% set excludedIdsString = excludedIds | join(', not ') %}
                        {% set allEntries = craft.entries.section('products').id('and, not '~excludedIdsString) %}
                        <li>
                            <a href="{{ entry.url }}">{{ entry.title }}</a>
                        </li>
                    {% elseif entry.slug == 'product-applications' %}
                        {% set allEntries = craft.entries.section('aplications').all() %}
                        <li>
                            <a href="{{ entry.url }}">{{ entry.title }}</a>
                        </li>
                    {% else %}
                        <li>
                            <a href="{{ entry.url }}">{{ entry.title }}</a>
                        </li>
                    {% endif %}
                {% endnav %}
            </ul>
        </li>
        {% set page = craft.entries.id(1316).one() %}
        <li><a href="{{ page.url }}">{{ page.title }}</a></li>
        {% set contactPage = craft.entries.id(2255).one() %}
        <li><a href="{{ contactPage.url }}">{{ contactPage.title }}</a></li>
    </ul>
</nav>