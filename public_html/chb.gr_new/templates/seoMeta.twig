{# SEO Start #}
{# Tip: You can pass the handle of your SEO field - getSeo<PERSON>ield('mySeoFieldHandle') #}
{% set seo = getSeoField('seoMeta') %}
{% if seo is null or seo.title == '' %}
    {% set seo = craft.seo.custom(siteName, '', false) %}
{% endif -%}

{% set locale = craft.app.locale %}
{% set locales = craft.app.i18n.siteLocaleIds|without(locale) %}


{% set dynDesc = '' %}
{% set dynImg = '' %}
{% if entry is defined %}

    {# Dynamic Description #}
    {% if entry.body|length %}
        {% set dynDesc = entry.body|wordLimit(20) %}
    {% elseif entry.body2|length %}
        {% set dynDesc = entry2.body|wordLimit(20) %}
    {% endif %}

    {# Dynamic Image #}
    {% if entry.featuredImageFull|length %}
        {% set dynImg = entry.featuredImageFull.one() %}
        {% set dynImg = dynImg.getUrl() %}
     {% elseif entry.featuredImageList|length %}
        {% set dynImg = entry.featuredImageListe.one() %}
        {% set dynImg = dynImg.getUrl() %}
    {% endif %}

{% endif %}

<title>{{ seo.title }}</title>

<meta name="description" content="{% if seo.description|length %} {{ seo.description }} {% else %} {{ dynDesc }} {% endif %}" />

{% set fb = seo.social.facebook -%}
<meta property="fb:app_id" content="{{ fb.handle }}">
<meta property="og:url" content="{{ craft.app.request.absoluteUrl }}" />
<meta property="og:type" content="website" />
<meta property="og:title" content="{{ fb.title }}" />
<meta property="og:image" content="{% if fb.image %} {{ craft.seo.facebookImage(fb.image) }} {% else %} {{ dynImg }} {% endif %}" />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta property="og:description" content="{% if fb.description|length %} {{ fb.description }} {% else %} {{ dynDesc }} {% endif %}" />
<meta property="og:site_name" content="{{ siteName }}" />
<meta property="og:locale" content="{{ locale|replace('-', '_') }}" />
{% for locale in locales -%}
    <meta property="og:locale:alternate" content="{{ locale|replace('-', '_') }}" />
{% endfor %}
	{% set tw = seo.social.twitter -%}
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:site" content="{{ tw.handle }}" />
<meta name="twitter:url" content="{{ craft.app.request.absoluteUrl }}" />
<meta name="twitter:title" content="{{ tw.title }}" />
<meta name="twitter:description" content="{% if tw.description|length %} {{ tw.description }} {% else %} {{ dynDesc }} {% endif %}" />
<meta name="twitter:image" content="{% if tw.image %} {{ craft.seo.twitterImage(tw.image) }} {% else %} {{ dynImg }} {% endif %}" />

{% if seo.robots -%}
    <meta name="robots" content="{{ seo.robots }}" />
{% endif %}
	{%- if seo.expiry -%}
        <meta name="robots" content="unavailable_after: {{ seo.expiry }}" />
    {% endif %}

<link rel="home" href="{{ siteUrl }}" />
<link rel="canonical" href="{{ seo.canonical }}">
{# SEO End #}