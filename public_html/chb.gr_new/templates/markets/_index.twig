{% extends "_layouts/site" %}

{% set title = entry.title %}
{% set bodyClass = '' %}

{% set allEntries = craft.entries.section('markets').all() %}

{% block body %}

    {% set featuredImage = entry.featuredImageFull.one() %}

    <div class="breadcrumb-area text-center" style="background: url({% if featuredImage %} {{ featuredImage ? featuredImage.getUrl() : ''}} {% endif %}) top center no-repeat; background-size:contain;">
        <div class="container">
            <h1>{{ entry.title }}</h1>
            <nav aria-label="breadcrumb">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"> &nbsp;</li>
                </ul>
            </nav>
        </div>
    </div>
    {% if entry.body|length %}
    <div class="container" style="padding-top:60px;">
        <div class="banner-area__excerpt2">
            {{ entry.body }}
        </div>
    </div>
    {% endif %}


    {% if allEntries|length %}
        {% for entry in allEntries %}
            {% set featuredImage = entry.featuredImageFull.one() %}
            <div class="banner-area fix banner-two-area {{ loop.index is even ? 'bg-1 alt ':'' }}">
                <div class="pt-70 pb-190 banner-area__bg"  {#style="background-image:url({{ featuredImage ? featuredImage.getUrl('fullFeaturedImage') : ''}});"#}>

                    <div class="featuredImage"
                         style="background: rgba(0, 0, 0, 0) url({{ featuredImage ? featuredImage.getUrl('fullFeaturedImage') : ''}}) no-repeat scroll left center; background-size:contain;"
                         {% if loop.index == 1 %}
                             data-aos="fade-right"
                             data-aos-offset="200"
                             data-aos-delay="50"
                             data-aos-duration="1000"
                             data-aos-easing="ease-in-out"
                             data-aos-mirror="true"
                             data-aos-once="true"
                             data-aos-anchor-placement="top-left"
                         {% else %}
                             data-aos="fade-up"
                             data-aos-offset="200"
                             data-aos-delay="50"
                             data-aos-duration="1000"
                             data-aos-easing="ease-in-out"
                             data-aos-mirror="true"
                             data-aos-once="true"
                             data-aos-anchor-placement="top-left"
                         {% endif %}
                    >
                    </div>


                    <div class="container"
                         data-aos="fade-up"
                         data-aos-offset="20"
                         data-aos-delay="100"
                         data-aos-duration="800"
                         data-aos-easing="ease-in-out"
                         data-aos-mirror="true"
                         data-aos-once="true"
                         data-aos-anchor-placement="top-left"
                    >
                        <div class="row banner-area__row">
                            <div class="col-md-8">
                                <div class="banner-text pt-130">
                                    <div class="heading">
{#                                      <span class="UpBorder" style="width: 140px;"></span>#}
                                        <h2 class="h1">
                                            {{ entry.title }}
                                            <div class="iconLeaf"></div>
                                        </h2>
{#                                        <span class="DownBorder" style="width: 140px;"></span>#}
                                    </div>
                                    <div class="banner-area__excerpt">
                                        {% if entry.body|length %}{{ entry.body }}  {% endif %}
                                        <a href="{{ entry.url }}" class="default-btn banner-area__btn">{{ 'Read more'|t }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% endif %}

{% endblock %}
