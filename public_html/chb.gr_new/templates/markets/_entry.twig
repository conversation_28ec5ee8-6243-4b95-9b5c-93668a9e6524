{% extends "_layouts/site" %}

{% set title = entry.title %}
{% set bodyClass = '' %}
{#{%#}
{#    set seo = craft.seo.custom(#}
{#        entry.title,#}
{#        entry.body|wordLimit(20),#}
{#    )#}
{#%}#}

{% block body %}

    {% set featuredImage = entry.featuredImageFull.one() %}
    <div class="banner-area fix banner-two-area bg-1 pt-95">
        <div class="pt-70 pb-190"  {#style="background: rgba(0, 0, 0, 0) url({{ featuredImage ? featuredImage.getUrl('fullFeaturedImage') : ''}}) no-repeat scroll left center; background-size:contain;"#}>

            <div class="featuredImage"
                 style="background: rgba(0, 0, 0, 0) url({{ featuredImage ? featuredImage.getUrl('fullFeaturedImage') : ''}}) no-repeat scroll left center; background-size:contain;"
                 data-aos="fade-right"
                 data-aos-offset="200"
                 data-aos-delay="50"
                 data-aos-duration="1000"
                 data-aos-easing="ease-in-out"
                 data-aos-mirror="true"
                 data-aos-once="false"
                 data-aos-anchor-placement="top-left"
            >
            </div>

            <div class="container" style="max-width:900px;"
                 data-aos="fade-top"
                 data-aos-offset="200"
                 data-aos-delay="50"
                 data-aos-duration="1000"
                 data-aos-easing="ease-in-out"
                 data-aos-mirror="true"
                 data-aos-once="false"
                 data-aos-anchor-placement="top-left"
            >
                <div class="row" style="justify-content: end">
                    <div class="col-md-8">
                        <div class="banner-text pt-130">
                            <div class="heading">
{#                                <span class="UpBorder" style="width: 140px;"></span>#}
                                <h2 class="h1">
                                    {{ entry.title }}
                                    <div class="iconLeaf"></div>
                                </h2>
{#                                <span class="DownBorder" style="width: 140px;"></span>#}
                            </div>
                            {% if entry.body|length %}
                            <div class="banner-area__excerpt">{{ entry.body }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if entry.body2|length %}
    <div class="blog-area  pt-60">
        <div class="container">
            <div class="post-area">
                <div class="single-post-item" style="border-bottom: 0; margin-bottom: 0;">
                    <div class="single-post-info-text text-left">
                        <div class="relativeBorder text-center scale" style="">
                            <span class="UpBorder"></span>
                            <h2 class="fadingTop h1">{{ 'Innovative Ideas for the'|t }} {{ entry.title }}</h2>
                            <span class="DownBorder"></span>
                        </div>
                        <div class="banner-area__excerpt2">{{ entry.body2 }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}


    {% set relatedApplications = entry.aplicationsSolutions %}
    {% if relatedApplications|length %}
    <div class="blog-area pb-95 pt-60">
        <div class="container">
            <div class="section-title text-center mb-50">
{#                <div class="section-img d-flex justify-content-center mb-30">#}
{#                    <img src="{{ siteUrl }}assets/_/img/Peach.jpg" alt="" width="150">#}
{#                </div>#}
                <h2>
                    <span>{{ 'Applications'|t }}  </span>
                    {#exclude in horeca#}
                    {% if entry.id != 134 %}
                    & {{ 'Solutions'|t }}
                    {% endif %}
                </h2>
            </div>
        </div>
        <div class="container" style="">
            <div class="row-flex">
                {% for rel in relatedApplications %}
                <div class="col-flex-4 text-center">
                    <div class="single-blog single-blog2"
                         data-aos="zoom-in-up"
                         data-aos-offset="30"
                         data-aos-delay="50"
                         data-aos-duration="700"
                         data-aos-easing="ease-in-out"
                         data-aos-mirror="true"
                         data-aos-once="true"
                         data-aos-anchor-placement="top-left"
                    >
                        {% if rel.alternativeLink|length %}
                            {% set url = rel.alternativeLink %}
                        {% else %}
                            {% set url = rel.url %}
                        {% endif %}
                        <div class="blog-image">
                            {% set relFeaturedImage = rel.featuredImageList.one() %}
                            <a href="{{ url }}"><img src="{{ relFeaturedImage ? relFeaturedImage.getUrl('listFeaturedImage') : alias('@assetBaseUrl/img/blog/1.jpg')}}" alt=""></a>
                        </div>
                        <div class="blog-text" style="border: 1px solid #e8e8e8; ">
                            <h4>{{ rel.title }}</h4>
                            {% if rel.body|length %}
                                <p>{{ rel.body }}</p>
                            {% endif %}
                            {% if rel.alternativeLink|length %}
                            <a href="{{ url }}" class="default-btn">{{ 'Read more'|t }}</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    {% set relatedProducts = entry.productPortfolio %}
    {% if relatedProducts|length %}
        <div class="blog-area pb-95 pt-60">
            <div class="container">
                <div class="section-title text-center mb-50">
                    <div class="section-img d-flex justify-content-center">
{#                        <img src="{{ siteUrl }}assets/img/icon/title.png" alt="">#}
                    </div>
                    <h2><span>{{ 'Product'|t }}  </span>& {{ 'Portfolio'|t }}</h2>
                </div>
            </div>
            <div class="container" style="">
                <div class="row-flex">
                    {% for rel in relatedProducts %}
                        <div class="col-flex-4 text-center">
                            <div class="single-blog single-blog2"
                                data-aos="zoom-in-up"
                                data-aos-offset="30"
                                data-aos-delay="50"
                                data-aos-duration="700"
                                data-aos-easing="ease-in-out"
                                data-aos-mirror="true"
                                data-aos-once="true"
                                data-aos-anchor-placement="top-left"

                            >
                                {% if rel.alternativeLink|length %}
                                    {% set url = rel.alternativeLink %}
                                {% else %}
                                    {% set url = rel.url %}
                                {% endif %}
                                <div class="blog-image">
                                    {% set relFeaturedImage = rel.featuredImageList.one() %}
                                    <a href="{{ url }}"><img src="{{ relFeaturedImage ? relFeaturedImage.getUrl('listFeaturedImage') : alias('@assetBaseUrl/img/blog/1.jpg')}}" alt=""></a>
                                </div>
                                <div class="blog-text" style="border: 1px solid #e8e8e8; ">
                                    <h4><a href="{{ url }}">{{ rel.title }}</a></h4>
                                    {% if rel.body|length %}
                                        <p>{{ rel.body }}</p>
                                    {% endif %}
                                    <a href="{{ url }}" class="default-btn">{{ 'Read more'|t }}</a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

{% endblock %}
