enableVersioning: true
handle: products
name: 'Product Portfolio'
propagationMethod: all
siteSettings:
  9bf607e2-1280-4405-ba1f-ee8488e1ed87:
    enabledByDefault: true
    hasUrls: true
    template: products\_entry
    uriFormat: 'product-portfolio/{slug}'
  af94e83e-d173-4704-82af-76ba368e79cb:
    enabledByDefault: true
    hasUrls: true
    template: products\_entry
    uriFormat: 'product-portfolio/{slug}'
  f1c8beb3-9cf5-407f-becd-9785d339ee43:
    enabledByDefault: true
    hasUrls: true
    template: products\_entry
    uriFormat: 'product-portfolio/{slug}'
structure:
  maxLevels: null
  uid: 23701387-b80d-4c5a-920a-5d7e5b8df784
type: structure
