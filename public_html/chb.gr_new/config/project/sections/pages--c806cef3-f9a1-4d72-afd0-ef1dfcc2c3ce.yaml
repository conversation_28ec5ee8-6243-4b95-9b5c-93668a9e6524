enableVersioning: true
handle: pages
name: Pages
propagationMethod: all
siteSettings:
  9bf607e2-1280-4405-ba1f-ee8488e1ed87:
    enabledByDefault: true
    hasUrls: true
    template: pages/_entry
    uriFormat: '{entry.last().uri}/{parent.uri}/{slug}'
  af94e83e-d173-4704-82af-76ba368e79cb:
    enabledByDefault: true
    hasUrls: true
    template: pages/_entry
    uriFormat: '{entry.last().uri}/{parent.uri}/{slug}'
  f1c8beb3-9cf5-407f-becd-9785d339ee43:
    enabledByDefault: true
    hasUrls: true
    template: pages/_entry
    uriFormat: '{entry.last().uri}/{parent.uri}/{slug}'
structure:
  maxLevels: null
  uid: 71692c9c-a49a-46af-9e13-d7abd44f0fc6
type: structure
