contentColumnType: string
fieldGroup: 48d33f00-f11d-413d-adcb-8b5b28337584
handle: featuredImageFull
instructions: ''
name: 'Featured Image Full'
searchable: true
settings:
  allowedKinds: null
  defaultUploadLocationSource: 'volume:b81cfe41-b5e5-4d84-9bf0-8530e0545f35'
  defaultUploadLocationSubpath: '{section.handle}/{parent.uri}/{slug}'
  limit: '1'
  localizeRelations: false
  restrictFiles: ''
  selectionLabel: 'Add an image'
  singleUploadLocationSource: 'volume:95641e86-d2b7-473d-86df-2f3da5c9e8bf'
  singleUploadLocationSubpath: ''
  source: null
  sources: '*'
  targetSiteId: null
  useSingleFolder: ''
  validateRelatedElements: ''
  viewMode: large
translationKeyFormat: null
translationMethod: site
type: craft\fields\Assets
