field: 0f5293e3-1b2c-4332-b87a-08d1974bfed3
fieldLayouts:
  501f53d3-5db7-449a-bcdc-e5a86e5f2b06:
    tabs:
      -
        elements:
          -
            fieldUid: fe7f0200-fc15-4712-bafa-291afa43993d
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: 22f6aa02-dc4c-429d-945a-fde648166c9a
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: 6a890501-f6aa-4a15-b644-080c1c730132
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: a46c83f8-f22e-4ef8-9644-a2178a5e5e23
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
        name: Content
        sortOrder: 1
fields:
  22f6aa02-dc4c-429d-945a-fde648166c9a:
    contentColumnType: text
    fieldGroup: null
    handle: boxLink
    instructions: ''
    name: 'Box Link'
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: ''
      columnType: text
      initialRows: '4'
      multiline: ''
      placeholder: ''
      uiMode: normal
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\PlainText
  6a890501-f6aa-4a15-b644-080c1c730132:
    contentColumnType: text
    fieldGroup: null
    handle: boxText
    instructions: ''
    name: 'Box  Text'
    searchable: true
    settings:
      availableTransforms: '*'
      availableVolumes: '*'
      cleanupHtml: true
      columnType: text
      configSelectionMode: choose
      defaultTransform: ''
      manualConfig: ''
      purifierConfig: ''
      purifyHtml: '1'
      redactorConfig: ''
      removeEmptyTags: '1'
      removeInlineStyles: '1'
      removeNbsp: '1'
      showHtmlButtonForNonAdmins: '1'
      showUnpermittedFiles: false
      showUnpermittedVolumes: true
      uiMode: enlarged
    translationKeyFormat: null
    translationMethod: site
    type: craft\redactor\Field
  a46c83f8-f22e-4ef8-9644-a2178a5e5e23:
    contentColumnType: string
    fieldGroup: null
    handle: boxImage
    instructions: ''
    name: 'Box Image'
    searchable: true
    settings:
      allowSelfRelations: false
      allowedKinds: null
      defaultUploadLocationSource: 'volume:b81cfe41-b5e5-4d84-9bf0-8530e0545f35'
      defaultUploadLocationSubpath: ''
      limit: '1'
      localizeRelations: false
      restrictFiles: ''
      selectionLabel: ''
      showSiteMenu: true
      showUnpermittedFiles: false
      showUnpermittedVolumes: true
      singleUploadLocationSource: 'volume:95641e86-d2b7-473d-86df-2f3da5c9e8bf'
      singleUploadLocationSubpath: ''
      source: null
      sources: '*'
      targetSiteId: null
      useSingleFolder: false
      validateRelatedElements: false
      viewMode: large
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Assets
  fe7f0200-fc15-4712-bafa-291afa43993d:
    contentColumnType: text
    fieldGroup: null
    handle: boxTitle
    instructions: ''
    name: 'Box Title'
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: ''
      columnType: text
      initialRows: '4'
      multiline: ''
      placeholder: ''
      uiMode: normal
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\PlainText
handle: boxItem
name: 'Box Item'
sortOrder: 1
