dateModified: 1611586129
email:
  fromEmail: <EMAIL>
  fromName: 'Christodoulou Bros SA'
  replyToEmail: <EMAIL>
  template: null
  transportSettings:
    password: Win222rtn123
    timeout: '10'
    username: <EMAIL>
  transportType: craft\mail\transportadapters\Gmail
graphql:
  publicToken:
    enabled: false
    expiryDate: null
plugins:
  contact-form:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
    settings:
      allowAttachments: ''
      prependSender: 'On behalf of'
      prependSubject: 'New message from Christodoulou Bros SA'
      successFlashMessage: 'Your message has been sent.'
      toEmail: '<EMAIL>,pgkat<PERSON><EMAIL>'
  feed-me:
    edition: standard
    enabled: true
    schemaVersion: 4.3.0
  logs:
    edition: standard
    enabled: true
    schemaVersion: 3.0.0
  redactor:
    edition: standard
    enabled: true
    schemaVersion: 2.3.0
  seo:
    edition: standard
    enabled: true
    schemaVersion: 3.1.1
    settings:
      description: ''
      facebookAppId: ''
      metaTemplate: seoMeta
      removeAlternateUrls: ''
      robots:
        - ''
        - ''
        - ''
        - ''
        - ''
        - ''
      robotsTxt: "{# Sitemap URL #}\r\nSitemap: {{ url(seo.sitemapName ~ '.xml') }}\r\n\r\n{# Disallows #}\r\n{% if craft.app.config.env != 'production' %}\r\n\r\n{# Disallow access to everything when NOT in production #}\r\nUser-agent: *\r\nDisallow: /\r\n\r\n{% else %}\r\n\r\n{# Disallow access to cpresources/ when live #}\r\nUser-agent: *\r\nDisallow: /cpresources/\r\n\r\n{% endif %}"
      sitemapLimit: '1000'
      sitemapName: sitemap
      socialImage: ''
      title:
        -
          __assoc__:
            -
              - key
              - '1'
            -
              - locked
              - '0'
            -
              - template
              - '{title}'
        -
          __assoc__:
            -
              - key
              - '2'
            -
              - locked
              - '1'
            -
              - template
              - ' - {{ siteName }}'
      titleSuffix: null
      twitterHandle: ''
  site-switcher:
    edition: standard
    enabled: true
    schemaVersion: 0.0.0
  typogrify:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
system:
  edition: solo
  live: true
  name: 'Christodoulou Bros SA'
  schemaVersion: 3.5.13
  timeZone: America/Los_Angeles
users:
  allowPublicRegistration: false
  defaultGroup: null
  photoSubpath: ''
  photoVolumeUid: null
  requireEmailVerification: true
