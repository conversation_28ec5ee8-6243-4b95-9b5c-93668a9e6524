#!/usr/bin/env php
<?php

/*
 * This file is part of Psy Shell.
 *
 * (c) 2012-2017 <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

// Try to find an autoloader for a local psysh version.
// We'll wrap this whole mess in a Closure so it doesn't leak any globals.
call_user_func(function () {
    $cwd = null;

    // Find the cwd arg (if present)
    $argv = isset($_SERVER['argv']) ? $_SERVER['argv'] : array();
    foreach ($argv as $i => $arg) {
        if ($arg === '--cwd') {
            if ($i >= count($argv) - 1) {
                fwrite(STDERR, 'Missing --cwd argument.' . PHP_EOL);
                exit(1);
            }
            $cwd = $argv[$i + 1];
            break;
        }

        if (preg_match('/^--cwd=/', $arg)) {
            $cwd = substr($arg, 6);
            break;
        }
    }

    // Or fall back to the actual cwd
    if (!isset($cwd)) {
        $cwd = getcwd();
    }

    $cwd = str_replace('\\', '/', $cwd);

    $chunks = explode('/', $cwd);
    while (!empty($chunks)) {
        $path = implode('/', $chunks);
        $prettyPath = $path;
        if (isset($_SERVER['HOME']) && $_SERVER['HOME']) {
            $prettyPath = preg_replace('/^' . preg_quote($_SERVER['HOME'], '/') . '/', '~', $path);
        }

        // Find composer.json
        if (is_file($path . '/composer.json')) {
            if ($cfg = json_decode(file_get_contents($path . '/composer.json'), true)) {
                if (isset($cfg['name']) && $cfg['name'] === 'psy/psysh') {
                    // We're inside the psysh project. Let's use the local Composer autoload.
                    if (is_file($path . '/vendor/autoload.php')) {
                        if (realpath($path) !== realpath(__DIR__ . '/..')) {
                            fwrite(STDERR, 'Using local PsySH version at ' . $prettyPath . PHP_EOL);
                        }

                        require $path . '/vendor/autoload.php';
                    }

                    return;
                }
            }
        }

        // Or a composer.lock
        if (is_file($path . '/composer.lock')) {
            if ($cfg = json_decode(file_get_contents($path . '/composer.lock'), true)) {
                foreach (array_merge($cfg['packages'], $cfg['packages-dev']) as $pkg) {
                    if (isset($pkg['name']) && $pkg['name'] === 'psy/psysh') {
                        // We're inside a project which requires psysh. We'll use the local Composer autoload.
                        if (is_file($path . '/vendor/autoload.php')) {
                            if (realpath($path . '/vendor') !== realpath(__DIR__ . '/../../..')) {
                                fwrite(STDERR, 'Using local PsySH version at ' . $prettyPath . PHP_EOL);
                            }

                            require $path . '/vendor/autoload.php';
                        }

                        return;
                    }
                }
            }
        }

        array_pop($chunks);
    }
});

// We didn't find an autoloader for a local version, so use the autoloader that
// came with this script.
if (!class_exists('Psy\Shell')) {
/* <<< */
    if (is_file(__DIR__ . '/../vendor/autoload.php')) {
        require __DIR__ . '/../vendor/autoload.php';
    } elseif (is_file(__DIR__ . '/../../../autoload.php')) {
        require __DIR__ . '/../../../autoload.php';
    } else {
        fwrite(STDERR, 'PsySH dependencies not found, be sure to run `composer install`.' . PHP_EOL);
        fwrite(STDERR, 'See https://getcomposer.org to get Composer.' . PHP_EOL);
        exit(1);
    }
/* >>> */
}

// If the psysh binary was included directly, assume they just wanted an
// autoloader and bail early.
//
// Keep this PHP 5.3 and 5.4 code around for a while in case someone is using a
// globally installed psysh as a bin launcher for older local versions.
if (version_compare(PHP_VERSION, '5.3.6', '<')) {
    $trace = debug_backtrace();
} elseif (version_compare(PHP_VERSION, '5.4.0', '<')) {
    $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
} else {
    $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
}

if (Psy\Shell::isIncluded($trace)) {
    unset($trace);

    return;
}

// Clean up after ourselves.
unset($trace);

// If the local version is too old, we can't do this
if (!function_exists('Psy\bin')) {
    $argv = isset($_SERVER['argv']) ? $_SERVER['argv'] : array();
    $first = array_shift($argv);
    if (preg_match('/php(\.exe)?$/', $first)) {
        array_shift($argv);
    }
    array_unshift($argv, 'vendor/bin/psysh');

    fwrite(STDERR, 'A local PsySH dependency was found, but it cannot be loaded. Please update to' . PHP_EOL);
    fwrite(STDERR, 'the latest version, or run the local copy directly, e.g.:' . PHP_EOL);
    fwrite(STDERR, PHP_EOL);
    fwrite(STDERR, '    ' . implode(' ', $argv) . PHP_EOL);
    exit(1);
}

// And go!
call_user_func(Psy\bin());
