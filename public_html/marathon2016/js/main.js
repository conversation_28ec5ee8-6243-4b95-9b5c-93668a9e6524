// remap jQuery to $
(function($){
	////////////////////////////////////////////////////////////
	$(document).ready(function (){
		
		// Preloader
		$(".loading").on("pageLoaded", function() {			
			$("#page").fadeTo( "slow" , 1, function() {				
			});
		});
		
		// CSS
		$('ul li:first-child').addClass('first');
		$('ul li:last-child').addClass('last');	
		
		$(".video-cont").fitVids();
		
		rearangeElements();						
		
	});
	////////////////////////////////////////////////////////////
	$(window).load(function() {
		
	});
	////////////////////////////////////////////////////////////
	$(window).resize(function() {
		rearangeElements();	
	});
	////////////////////////////////////////////////////////////
	$(window).bind('orientationchange', function(event) {
	 	rearangeElements();	
	});	
	////////////////////////////////////////////////////////////	
	function rearangeElements() {		
		
	};
	////////////////////////////////////////////////////////////
})(window.jQuery);