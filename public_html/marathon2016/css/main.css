/*
 *  Project Styles
 * 
 */
/* ==========================================================================
   Fonts
   ========================================================================== */

	@font-face {
		font-family: 'pf_beausans_probold';
		src: url('../fonts/pfbeausanspro-bold-webfont.eot');
		src: url('../fonts/pfbeausanspro-bold-webfont.eot?#iefix') format('embedded-opentype'),
			 url('../fonts/pfbeausanspro-bold-webfont.woff') format('woff'),
			 url('../fonts/pfbeausanspro-bold-webfont.ttf') format('truetype');
		font-weight: normal;
		font-style: normal;
	}
	@font-face {
		font-family: 'pf_beausans_proregular';
		src: url('../fonts/pfbeausanspro-reg-webfont.eot');
		src: url('../fonts/pfbeausanspro-reg-webfont.eot?#iefix') format('embedded-opentype'),
			 url('../fonts/pfbeausanspro-reg-webfont.woff') format('woff'),
			 url('../fonts/pfbeausanspro-reg-webfont.ttf') format('truetype');
		font-weight: normal;
		font-style: normal;
	}
	@font-face {
	  font-family: 'PFReminderPro-Regular';
	  src: url('../fonts/PFReminderPro-Regular.eot?#iefix') format('embedded-opentype'),  url('../fonts/PFReminderPro-Regular.woff') format('woff'), url('../fonts/PFReminderPro-Regular.ttf')  format('truetype'), url('../fonts/PFReminderPro-Regular.svg#PFReminderPro-Regular') format('svg');
	  font-weight: normal;
	  font-style: normal;
	}

/* ==========================================================================
   Helper classes
   ========================================================================== */
   
	.container { margin: 0 auto; position: relative; width: 950px; }
	.container:before, .container:after { content: " "; display: table; }
	.container:after { clear: both; }
	.container { *zoom: 1; }
	.container-fluid { width: 100%; padding: 0 15px; }
	
	.clearfix:before,
	.clearfix:after { content: " "; display: table; }	
	.clearfix:after { clear: both; }
	
	.visuallyhidden { border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; }
	.visuallyhidden.focusable:active,
	.visuallyhidden.focusable:focus { clip: auto; height: auto; margin: 0; overflow: visible; position: static; width: auto; }

	img { max-width: 100%; }
	ul, li { list-style: none; padding: 0; margin: 0; }
	p { margin: 0; padding: 0; }
	h1,h2,h3,h4,h5,h6 { font-weight: normal; margin: 0; line-height: 1em; padding: 0; }
	
	a { text-decoration: none; color: #DC1928; }
	
	.browserupgrade { margin: 0.2em 0; background: #ccc; color: #000; padding: 0.2em 0; } 
	
	.hidden-desktop { display: none; }
	
	.block {display:block;}
	.table {display:table;}
	.table-cell {display:table-cell; vertical-align:middle;}	
	.text-right {text-align:right;}
	.text-center {text-align:center;}	
	.relative {position:relative;}
	.pull-left {float:left;}
	.pull-right {float:right;}
	.cover {background-position:center center; background-repeat:no-repeat; background-size:cover;}

/* ==========================================================================
   Grid
   ========================================================================== */

	* { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }
	
	.row { margin: 0 -20px; }
	.row:before, .row:after { content: " "; display: table; }
	.row:after { clear: both; }
	.row { *zoom: 1; }
	.row>div { padding: 0 20px; }
	
	.col12{width:100%;float:left;}
	.col11{width:91.666666666667%;float:left;}
	.col10{width:83.333333333333%;float:left;}
	.col9{width:75%;float:left;}
	.col8{width:66.666666666667%;float:left;}
	.col7{width:58.333333333333%;float:left;}
	.col6{width:50%;float:left;}
	.col5{width:41.666666666667%;float:left;}
	.col4{width:33.333333333333%;float:left;}
	.col3{width:25%;float:left;}
	.col2{width:16.666666666667%;float:left;}
	.col1{width:8.3333333333333%;float:left;}
	
	.col20{width:20%;float:left;}
	.col30{width:30%;float:left;}
	.col40{width:40%;float:left;}
	.col60{width:60%;float:left;}
	.col70{width:70%;float:left;}
	.col80{width:80%;float:left;}
	.col90{width:90%;float:left;}

/* ==========================================================================
    General styles
   ========================================================================== */
   
	html { background:#fff;  -webkit-text-size-adjust: 100%; height:100%;}
	body { font-family: 'pf_beausans_proregular'; font-size:17px; line-height:1.4; color:#1B1C20; height:100%; }
	
	a { text-decoration:none; -webkit-transition: all 400ms ease; -moz-transition: all 400ms ease; -ms-transition: all 400ms ease; -o-transition: all 400ms ease; transition: all 400ms ease; outline:none!important; }
	a:hover { color:#000;}
	
	input::-webkit-input-placeholder, textarea::-webkit-input-placeholder { color: #834e2e; opacity:1; }
	input:-moz-placeholder, textarea:-moz-placeholder{ color: #834e2e; opacity:1; }
	input::-moz-placeholder, textarea::-moz-placeholder { color: #834e2e; opacity:1; }
	input:-ms-input-placeholder, textarea:-ms-input-placeholder { color: #834e2e; opacity:1; }

   
/* ==========================================================================
    Preloader
   ========================================================================== */	
   
    #page {opacity:0; }
	.bg{height:100%;background:url(../images/bg.jpg) center center no-repeat; background-size:cover; background-attachment:fixed; width:100%; position:fixed; top:0; bottom:0; left:0; right:0; width:100%; height:100%; z-index:1;}
	.loading {display:block;/*background-color:#fff;*/color:333;top:0;bottom:0;width:100%;height:100%;text-align:center;position:fixed;z-index:6000;}	
	.loading .bar {top:46%;left:40%;background-color:#999;width:20%;color:#333;font-size:20px;height:5px;text-align:center;position:absolute;}
	.loading .percent { /* Preload percentage bar */background-color:#000;width:2%;	height:5px;}
	.loading span {	top:49%;left:0%;width:100%;color:#000;font-size:15px;text-align:center;z-index:6000;position:absolute;}

/* ==========================================================================
    Main
   ========================================================================== */
	
	.logo{ display:block; width:200px; position:fixed; top:0px; left:12%;z-index:2;}		
	.main {max-width:520px; width:50%; position:absolute; top:0; right:10%;z-index:3;}
	
	.blue-panel {background:rgb(0,143,213);background:rgba(0,143,213,0.5); padding:40px; font-family: 'PFReminderPro-Regular'; font-size:30px; line-height:1.4; color:#fff;}
	
	.white-panel {padding:40px; background:#fff;}
	
	strong {font-family: 'pf_beausans_probold'; font-weight:normal;}
	p {margin-bottom:25px;}
	h3 {font-family: 'PFReminderPro-Regular'; font-size:28px; line-height:1.4; color:#E60004;margin-bottom:20px;}
	ul {font-family: 'pf_beausans_probold'; list-style:disc; padding-left:20px; margin-bottom:25px;}
	ul li {list-style:disc;}


	.video-cont {margin-bottom:25px;}
	
	.close-btn {color:#fff; font-size:40px; display:block; position:absolute; top:10px; left:20px; z-index:1000;}
	


/* ==========================================================================
    Media Queries
   ========================================================================== */
	/*
	 * Tablet
	 */
	@media only screen and (max-width: 1281px) {
		/* Tablet grid */
		.col-md-12{width:100%;}
		.col-md-11{width:91.666666666667%;}
		.col-md-10{width:83.333333333333%;}
		.col-md-9{width:75%;}
		.col-md-8{width:66.666666666667%;}
		.col-md-7{width:58.333333333333%;}
		.col-md-6{width:50%;}
		.col-md-5{width:41.666666666667%;}
		.col-md-4{width:33.333333333333%;}
		.col-md-3{width:25%;}
		.col-md-2{width:16.666666666667%;}
		.col-md-1{width:8.3333333333333%;}
	
		/* Helpers */
		.hidden-tablet { display: none; }
		.visible-tablet { display: block; }
		
		body { font-size:14px; line-height:1.4;}
		.logo{  width:150px;left:12%;}		
		.main {max-width:520px; width:50%; right:10%;}
		.blue-panel {padding:20px;font-size:24px; }
		.white-panel {padding:20px;}
		p {margin-bottom:15px;}
		h3 {font-size:24px; margin-bottom:10px;}
		ul { margin-bottom:15px;}
		.video-cont {margin-bottom:15px;}
		
		
	}
	
	/*
	 * Mobile
	 */
	@media only screen and (max-width: 767px) {
		
		/* Mobile grid */
		.col-sm-12{width:100%;}
		.col-sm-11{width:91.666666666667%;}
		.col-sm-10{width:83.333333333333%;}
		.col-sm-9{width:75%;}
		.col-sm-8{width:66.666666666667%;}
		.col-sm-7{width:58.333333333333%;}
		.col-sm-6{width:50%;}
		.col-sm-5{width:41.666666666667%;}
		.col-sm-4{width:33.333333333333%;}
		.col-sm-3{width:25%;}
		.col-sm-2{width:16.666666666667%;}
		.col-sm-1{width:8.3333333333333%;}
	
		/* Helpers */
		.hidden-mobile { display: none; }
		.visible-mobile { display: block; }
		
		body { font-size:14px; line-height:1.4;}
		.logo{  width:150px;left:0;}		
		.main {max-width:800px; width:100%; right:auto; top:auto; position:relative;  padding-left:150px;}
		.blue-panel {padding:20px;font-size:24px; }
		.white-panel {padding:20px;}
		p {margin-bottom:15px;}
		h3 {font-size:24px; margin-bottom:10px;}
		ul { margin-bottom:15px;}
		.video-cont {margin-bottom:15px;}
		
		.close-btn {top:0;}
		
		
	}   
	
	@media only screen and (max-width: 479px){
		
		/* Mobile grid */
		.col-xs-12{width:100%;}
		.col-xs-11{width:91.666666666667%;}
		.col-xs-10{width:83.333333333333%;}
		.col-xs-9{width:75%;}
		.col-xs-8{width:66.666666666667%;}
		.col-xs-7{width:58.333333333333%;}
		.col-xs-6{width:50%;}
		.col-xs-5{width:41.666666666667%;}
		.col-xs-4{width:33.333333333333%;}
		.col-xs-3{width:25%;}
		.col-xs-2{width:16.666666666667%;}
		.col-xs-1{width:8.3333333333333%;}
		
		
		body { font-size:14px; line-height:1.4;}
		.logo{  width:150px;left:0;z-index:5;position:absolute; left:50%; margin-left:-75px;}		
		.main {max-width:800px; width:100%; right:auto; top:auto; position:relative;  padding-left:0;}
		.blue-panel {padding:20px;font-size:24px; padding-top:170px;}
		.white-panel {padding:20px;}
		p {margin-bottom:15px;}
		h3 {font-size:24px; margin-bottom:10px;}
		ul { margin-bottom:15px;}
		.video-cont {margin-bottom:15px;}
		
	}
	
	@media 
	(-webkit-min-device-pixel-ratio: 2), 
	(min-resolution: 192dpi) { 	
		/* Retina-specific stuff here */
		
	}