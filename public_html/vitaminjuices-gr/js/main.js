// remap jQuery to $
(function($){
	////////////////////////////////////////////////////////////
	$(document).ready(function (){
		
		// CSS
		$('ul li:first-child').addClass('first');
		$('ul li:last-child').addClass('last');	
		
		rearangeElements();	
		
		// Preloader
		$(".loading").on("pageLoaded", function() {			
			$("#page").fadeTo( "slow" , 1, function() {				
			});
		});
		
		// Verttical Scrolling
		$('.nav li a').click(function(e) {	
			e.preventDefault();		
			
			if($($.attr(this, "href")).offset().top - $("#header").outerHeight() -40 < 0)
				targetScroll = 1;
			else
				targetScroll =$($.attr(this, "href")).offset().top - $("#header").outerHeight();
				
			
			$('html,body').scrollTo( targetScroll, 1000, {easing:'easeInOutSine', onAfter:function(target, settings){} } );	
			
			if($('#nav').hasClass('open'))
				$('#nav').toggleClass('open').slideUp();
					
		});
		
		
		$('.waypoint').each(function(index, element) {
            
			var section = $(this);
			var waypoint = new Waypoint({
			  element: section,
			  handler: function(direction) {
				//console.log(this.element.attr('id'))
				$('.nav li').removeClass('on');		
				$('.nav li a[href=#'+this.element.attr('id')+']').parent().addClass('on');	
				
			  },
			  offset: '50%'
			})
			
        });
		


		
		
		
		
		var config = {			
			reset:  false,
			scale:    { direction: 'up', power: '0%' },
			 vFactor:  0.40
		 }
		 window.sr = new scrollReveal( config );
		
		
		/**
		 * Do the clicking stuff
		 *
		 */
		
		(function() {
		  var cards = document.querySelectorAll(".card.effect__click");
		  for ( var i  = 0, len = cards.length; i < len; i++ ) {
			var card = cards[i];
			clickListener( card );
		  }
		
		  function clickListener(card) {
			card.addEventListener( "click", function() {
			  $(this).parent().find('.card').each(function( index ) {
				  var c = this.classList;
				  c.contains("flipped") === true ? c.remove("flipped") : c.add("flipped");
			  });
			  
			  
			  
			});
		  }
		})();
		
		/**
		 * Do the random stuff
		 *
		 */
		
		(function() {
		
		  // cache vars
		  var cards = document.querySelectorAll(".card.effect__random");
		  var timeMin = 1;
		  var timeMax = 3;
		  var timeouts = [];
		
		  // loop through cards
		  for ( var i = 0, len = cards.length; i < len; i++ ) {
			var card = cards[i];
			var cardID = card.getAttribute("data-id");
			var id = "timeoutID" + cardID;
			var time = randomNum( timeMin, timeMax ) * 1000;
			cardsTimeout( id, time, card );
		  }
		
		  // timeout listener
		  function cardsTimeout( id, time, card ) {
			if (id in timeouts) {
			  clearTimeout(timeouts[id]);
			}
			timeouts[id] = setTimeout( function() {
			  var c = card.classList;
			  var newTime = randomNum( timeMin, timeMax ) * 1000;
			  c.contains("flipped") === true ? c.remove("flipped") : c.add("flipped");
			  cardsTimeout( id, newTime, card );
			}, time );
		  }
		
		  // random number generator given min and max
		  function randomNum( min, max ) {
			return Math.random() * (max - min) + min;
		  }
		
		})();
		
		
		$('#menu-icon').click(function(e) {
            e.preventDefault();
			$('#nav').toggleClass('open').slideToggle();
        });
		
	});
	////////////////////////////////////////////////////////////
	$(window).load(function() {
		
	});
	////////////////////////////////////////////////////////////
	$(window).resize(function() {
		rearangeElements();	
	});
	////////////////////////////////////////////////////////////
	$(window).bind('orientationchange', function(event) {
	 	rearangeElements();	
	});
	////////////////////////////////////////////////////////////
	function isDesktop(){
		var check = !(Detectizr.device.type == "mobile") && !(Detectizr.device.type == "tablet");
		return check
	}
	function isMobile(){
		var check = (Detectizr.device.type == "mobile") && (Detectizr.device.orientation == "portrait");
		return check;
	}
	function isMobileLandscape() {
		var check = (Detectizr.device.type == "mobile") && (Detectizr.device.orientation == "landscape");
		return check;
	}
	function isTablet(){
		var check = Detectizr.device.type == "tablet";
		return check;
	}	
	////////////////////////////////////////////////////////////	
	function rearangeElements() {		
		$('.section.fullscreen').css('height', $(window).height()-$('#header').outerHeight()-40+'px');
		$('.section.half').css('height', ($(window).height()/2)+'px');
	};
	////////////////////////////////////////////////////////////
})(window.jQuery);