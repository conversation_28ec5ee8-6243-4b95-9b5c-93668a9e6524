/*
 *  Project Styles
 * 
 */

/* ==========================================================================
    Base styles and resets
   ========================================================================== */

	html{color:#000;background:#FFF;}body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0;}table{border-collapse:collapse;border-spacing:0;}fieldset,img{border:0;}address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal;}li{list-style:none;}caption,th{text-align:left;}h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal;}q:before,q:after{content:'';}abbr,acronym{border:0;font-variant:normal;}sup{vertical-align:text-top;}sub{vertical-align:text-bottom;}input,textarea,select{font-family:inherit;font-size:inherit;font-weight:inherit;}input,textarea,select{*font-size:100%;}legend{color:#000;}
	html{-webkit-text-size-adjust: none;}
	strong{ font-weight:bold;}
	img{ vertical-align:top;}
	.cl{ clear:both; font-size:0;}
	
	article, aside, details, figcaption, figure, footer, header, hgroup, nav, section { display: block; }
	audio, canvas, video { display: inline-block; *display: inline; *zoom: 1; }
	audio:not([controls]) { display: none; }
	[hidden] { display: none; }
	
	html { font-size: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
	html, button, input, select, textarea { font-family: sans-serif; color: #222; }
	body { margin: 0; font-size: 1em; line-height: 1.4; }
	
	a { color: #00e; }
	a:hover { color: #06e; }
	a:focus { outline: thin dotted; }
	a:hover, a:active { outline: 0; }
	abbr[title] { border-bottom: 1px dotted; }
	b, strong { font-weight: bold; }
	blockquote { margin: 1em 40px; }
	dfn { font-style: italic; }
	hr { display: block; height: 1px; border: 0; border-top: 1px solid #ccc; margin: 1em 0; padding: 0; }
	ins { background: #ff9; color: #000; text-decoration: none; }
	mark { background: #ff0; color: #000; font-style: italic; font-weight: bold; }
	pre, code, kbd, samp { font-family: monospace, serif; _font-family: 'courier new', monospace; font-size: 1em; }
	pre { white-space: pre; white-space: pre-wrap; word-wrap: break-word; }
	
	q { quotes: none; }
	q:before, q:after { content: ""; content: none; }
	small { font-size: 85%; }
	sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-align: baseline; }
	sup { top: -0.5em; }
	sub { bottom: -0.25em; }
	
	ul, ol { margin: 0; padding: 0; }
	li { list-style:none; margin:0; padding:0; }
	dd { margin: 0 0 0 40px; }
	nav ul, nav ol { list-style: none; list-style-image: none; margin: 0; padding: 0; }
	
	img { border: 0; -ms-interpolation-mode: bicubic; vertical-align: middle; }
	svg:not(:root) { overflow: hidden; }
	figure { margin: 0; }
	
	form { margin: 0; }
	fieldset { border: 0; margin: 0; padding: 0; }
	
	label { cursor: pointer; }
	legend { border: 0; *margin-left: -7px; padding: 0; white-space: normal; }
	button, input, select, textarea { font-size: 100%; margin: 0; vertical-align: baseline; *vertical-align: middle; }
	button, input { line-height: normal; }
	button, input[type="button"], input[type="reset"], input[type="submit"] { cursor: pointer; -webkit-appearance: button; *overflow: visible; }
	button[disabled], input[disabled] { cursor: default; }
	input[type="checkbox"], input[type="radio"] { box-sizing: border-box; padding: 0; *width: 13px; *height: 13px; }
	input[type="search"] { -webkit-appearance: textfield; -moz-box-sizing: content-box; -webkit-box-sizing: content-box; box-sizing: content-box; }
	input[type="search"]::-webkit-search-decoration, input[type="search"]::-webkit-search-cancel-button { -webkit-appearance: none; }
	button::-moz-focus-inner, input::-moz-focus-inner { border: 0; padding: 0; }
	textarea { overflow: auto; vertical-align: top; resize: vertical; }
	input:valid, textarea:valid {  }
	input:invalid, textarea:invalid { background-color: #f0dddd; }
	
	table { border-collapse: collapse; border-spacing: 0; }
	td { vertical-align: top; }
	
	.chromeframe { margin: 0.2em 0; background: #ccc; color: black; padding: 0.2em 0; }
	
	.clearfix:before, .clearfix:after { content: ""; display: table; }
	.clearfix:after { clear: both; }
	.clearfix { *zoom: 1; }
	
	input,textarea,select{-webkit-appearance:none;}


/* ==========================================================================
    General styles
   ========================================================================== */
	html { background:#fff; font-size:62.5%; -webkit-text-size-adjust: 100%; height:100%;}
	body { overflow-x:hidden;height:100%; font-family:'PFReminderPro-Regular';color: #7D7D7D;font-size: 21px;line-height: 31px;
	
	}
	
	a { text-decoration:none; -webkit-transition: all 400ms ease; -moz-transition: all 400ms ease; -ms-transition: all 400ms ease; -o-transition: all 400ms ease; transition: all 400ms ease; outline:none!important; }
	a:hover { color:#fff;}
	
	input::-webkit-input-placeholder, textarea::-webkit-input-placeholder { color: #834e2e; opacity:1; }
	input:-moz-placeholder, textarea:-moz-placeholder{ color: #834e2e; opacity:1; }
	input::-moz-placeholder, textarea::-moz-placeholder { color: #834e2e; opacity:1; }
	input:-ms-input-placeholder, textarea:-ms-input-placeholder { color: #834e2e; opacity:1; }
	
	* { -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
	
	img {max-width:100%; height:auto;}
	
	.wrapper { width:100%; max-width:1024px; padding:0 20px; margin:0 auto; position:relative; }
	.wrapper:before, .wrapper:after { content: ""; display: table; }
	.wrapper:after { clear: both; }
	.wrapper { *zoom: 1; }
	
	.row{ margin-left: -10px; margin-right: -10px; }
	.row:before, .row:after {display: table;content: "";line-height: 0;}
	.row:after {clear: both;}
	.row > div{ padding: 0 10px; }
	
	.col12 { width: 100%; float: left;}
	.col11 { width: 91.66666666666666%; float: left; }
	.col10 { width: 83.33333333333334%; float: left;}
	.col9 { width: 75%; float: left;}
	.col8 { width: 66.66666666666666%; float: left; }
	.col7 { width: 58.333333333333336%; float: left;}
	.col6 { width: 50%; float: left;}
	.col5 { width: 41.66666666666667%; float: left;}
	.col4 { width: 33.33333333333333%; float: left;}
	.col3 { width: 25%; float: left;}
	.col2 { width: 16.666666666666664%; float: left;}
	.col1 { width: 8.333333333333332%;  float: left;}
	
	#page {opacity:0; position:relative; z-index:1;}
	
	[data-sr] {visibility: hidden;}
	
	
	.side-gradient.left{position:fixed; z-index:0; top:0; bottom:0; left:0;  width:40%; height:100%;
		background: #e1e1e1;
		background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIxJSIgc3RvcC1jb2xvcj0iI2UxZTFlMSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgPC9saW5lYXJHcmFkaWVudD4KICA8cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2dyYWQtdWNnZy1nZW5lcmF0ZWQpIiAvPgo8L3N2Zz4=);
		background: -moz-linear-gradient(left,  #e1e1e1 1%, #ffffff 50%);
		background: -webkit-gradient(linear, left top, right top, color-stop(1%,#e1e1e1), color-stop(50%,#ffffff));
		background: -webkit-linear-gradient(left,  #e1e1e1 1%,#ffffff 50%);
		background: -o-linear-gradient(left,  #e1e1e1 1%,#ffffff 50%);
		background: -ms-linear-gradient(left,  #e1e1e1 1%,#ffffff 50%);
		background: linear-gradient(to right,  #e1e1e1 1%,#ffffff 50%);
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e1e1e1', endColorstr='#ffffff',GradientType=1 );
	
	}
	.side-gradient.right {position:fixed; z-index:0; top:0; bottom:0; right:0;  width:40%; height:100%;
		background: #ffffff;
		background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0b3AtY29sb3I9IiNmZmZmZmYiIHN0b3Atb3BhY2l0eT0iMSIvPgogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjZTFlMWUxIiBzdG9wLW9wYWNpdHk9IjEiLz4KICA8L2xpbmVhckdyYWRpZW50PgogIDxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9InVybCgjZ3JhZC11Y2dnLWdlbmVyYXRlZCkiIC8+Cjwvc3ZnPg==);
		background: -moz-linear-gradient(left,  #ffffff 50%, #e1e1e1 100%);
		background: -webkit-gradient(linear, left top, right top, color-stop(50%,#ffffff), color-stop(100%,#e1e1e1));
		background: -webkit-linear-gradient(left,  #ffffff 50%,#e1e1e1 100%);
		background: -o-linear-gradient(left,  #ffffff 50%,#e1e1e1 100%);
		background: -ms-linear-gradient(left,  #ffffff 50%,#e1e1e1 100%);
		background: linear-gradient(to right,  #ffffff 50%,#e1e1e1 100%);
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#e1e1e1',GradientType=1 );

	}
	
/* ==========================================================================
    Preloader
   ========================================================================== */	
   
	.loading {display:block;background-color:#fff;color:333;top:0;bottom:0;width:100%;height:100%;text-align:center;position:fixed;z-index:6000;}	
	.loading .bar {top:46%;left:40%;background-color:#999;width:20%;color:#333;font-size:20px;height:10px;text-align:center;position:absolute;}
	.loading .percent { /* Preload percentage bar */background-color:#C30009;width:2%;	height:10px;}
	.loading span {	top:49%;left:0%;width:100%;color:#C30009;font-family: 'Roboto', sans-serif; font-weight:900; font-weight:normal;font-size:20px;text-align:center;z-index:6000;position:absolute;}

/* ==========================================================================
    Fonts
   ========================================================================== */
	
	@font-face {
		font-family:'PFReminderPro-Regular';
		src: url('../fonts/PFReminderPro-Regular_gdi.eot');
		src: url('../fonts/PFReminderPro-Regular_gdi.eot?#iefix') format('embedded-opentype'),
			url('../fonts/PFReminderPro-Regular_gdi.woff') format('woff'),
			url('../fonts/PFReminderPro-Regular_gdi.ttf') format('truetype'),
			url('../fonts/PFReminderPro-Regular_gdi.svg#PFReminderPro-Regular') format('svg');
		font-weight: 400;
		font-style: normal;
		font-stretch: normal;
		unicode-range: U+0020-F6D4;
	}
	
	@font-face {
		font-family:'PFBeauSansPro-Bold';
		src: url('../fonts/PFBeauSansPro-Bold_gdi.eot');
		src: url('../fonts/PFBeauSansPro-Bold_gdi.eot?#iefix') format('embedded-opentype'),
			url('../fonts/PFBeauSansPro-Bold_gdi.woff') format('woff'),
			url('../fonts/PFBeauSansPro-Bold_gdi.ttf') format('truetype'),
			url('../fonts/PFBeauSansPro-Bold_gdi.svg#PFBeauSansPro-Bold') format('svg');
		font-weight: 700;
		font-style: normal;
		font-stretch: normal;
		unicode-range: U+0020-FB05;
	}

	
/* ==========================================================================
    Main
   ========================================================================== */	
   
   #header{ height:80px; background:#fff;border-bottom:1px solid #eaeaea; position:fixed; top:0; left:0; right:0; width:100%; z-index:999; -webkit-box-shadow: 0 0 5px 0 rgba(0,0,0,0.4);box-shadow: 0 0 5px 0 rgba(0,0,0,0.4);}
   #logo{ display:block; float:left; margin-top: 10px;max-width: 210px;}
	.nav{ list-style:none;  height:80px; float:right; border-left:1px solid #eaeaea;}
		.nav li{ list-style:none; display:block; height:100%; float:left;position:relative; }
			.nav li a{display:block; width:100%; height:100%; font-size:20px; line-height:17px; font-weight:300; color:#7d7d7d;  border-right:1px solid #eaeaea; padding:35px 20px 0px 20px;}
			.nav li.on a:after,
			.nav li a:hover:after {display:block; position:absolute; bottom:0px; left:0px; width:100%; content:" "; height:7px; background:#CE6323;}
			
			.nav li.on a[href=#section-portokali]:after,
			.nav li a[href=#section-portokali]:hover:after {background: #CE6323;}			
			.nav li.on a[href=#section-9Frouta]:after,
			.nav li a[href=#section-9Frouta]:hover:after {background: #E4B40D;}			
			.nav li.on a[href=#section-verikoko]:after,
			.nav li a[href=#section-verikoko]:hover:after {background: #CE6323;}			
			.nav li.on a[href=#section-rodi]:after,
			.nav li a[href=#section-rodi]:hover:after {background: #C94F59;}			
			.nav li.on a[href=#section-vissino]:after,
			.nav li a[href=#section-vissino]:hover:after {background: #803B40;}
			
   
    .table {display:table; width:100%; height:100%; }
    .table-cell {display:table-cell; vertical-align:middle;}			
	.section {margin:4% 0;}
	.section.half { background-color:rgba(0,0,0,0.8)!important;}
	
	.section.fullscreen {margin-top:0; /*padding-top:4%;*/}		
	
	.top100 {padding-top:100px;}
	
	.packs {width:100%; margin:0 auto 0 auto; }
	.pack { cursor:pointer;}
	
	 h2.section-heading {display: inline; background: rgba(194, 11, 75, 0.75) none repeat scroll 0% 0%; padding: 5px; font-family:'PFBeauSansPro-Bold'; font-size: 36px; line-height: 60px; font-weight:normal; color: #FFF; clear: both; }
	 
	 h1.page-title { font-family: 'Roboto', sans-serif; font-weight:900;font-size: 55px; line-height:55px; color: #CE6323;  clear: both; margin-bottom:0px; padding-top:150px; text-align:center; text-transform:uppercase;text-shadow: 1px 1px 0 #000000;}
	 
	/*.ribbon{height: 55px; background: transparent url("../images/ribbon.png") repeat-x scroll center top; }*/
	
	.divider {position:relative; text-align:center; margin:20px 0 40px 0;}
	.divider::after {content:''; display:block; width:100%; height:1px; background:#eaeaea; position:absolute; left:0; right:0; top:50%;  z-index:0;}
	.divider .divider-img {background-color:#fff; z-index:1; width:171px; margin:0 auto; position:relative; padding:0 10px;}
	
	
	.section-text {text-align:center; padding-bottom:40px;}
	h4 {font-family: 'Roboto', sans-serif; font-weight:900;color: #CE6323; font-size: 30px; line-height: 36px; margin-bottom:5px;text-shadow: 0.5px 0.5px 0 #000000;}
	.section-text  p {margin-bottom:20px;}
	.section-text .wrapper {max-width:400px;}
	
	#section-portokali h4 {color: #CE6323;}
	#section-9Frouta h4 {color: #E4B40D;}
	#section-verikoko h4 {color: #CE6323;}
	#section-rodi h4 {color: #C94F59;}
	#section-vissino h4 {color: #803B40;}
	
	.kapaki {margin:0 auto; max-width: 120px; width: 12%;opacity: 0.6; padding-top:10px;}
	.kapaki img {display:block;}
	
	.footer {position:fixed; bottom:0; left:0; right:0; width:100%; padding:10px 0; background:#fff; color:#000; font-family:Arial; z-index:600; font-size: 15px;line-height: 15px; border-bottom:1px solid #eaeaea; -webkit-box-shadow: 0 0 5px 0 rgba(0,0,0,0.4);box-shadow: 0 0 5px 0 rgba(0,0,0,0.4);}
	
	.see-more-link {font-family: 'Roboto', sans-serif; font-weight:900;color: #CE6323; font-size: 24px; line-height:24px;  display:block; margin:0 auto 40px auto; text-align:center;}
	.see-more-link:hover {color: #CE6323;text-shadow: 0.5px 0.5px 0 #000000;}
	
	/*.pack::after {content:''; display:block; padding-top:268%;}*/
	
	/**
	 * The cards
	 *
	 * Each card plays home to a front and back. I've set them up as squares here
	 * by using the padding technique, but you may set them up in any dimensions
	 * you like based on your site design.
	 */
	.card {
	  position: relative;	 
	  text-align: center;
	  border:1px solid #eaeaea; 
	  
	  overflow:hidden;
	  height:auto;
	  
	}
	.card::before {content:''; display:block; padding-top:278%;}
	
	
	.card:nth-child(1) {
	 /* margin-left: -3px;
	  margin-right: 1px;*/
	}
	
	.card:nth-child(2),
	.card:nth-child(3) {
	  /*margin-right: 1px;*/
	}
	
	/* card fronts and backs */
	.card__front,
	.card__back {
	  position: absolute;
	  top: 0;
	  left: 0;
	  right:0;
	  bottom:0;
	  width: 100%;
	  height: 100%;
	}
	
	.card__front,
	.card__back {
	  -webkit-backface-visibility: hidden;
			  backface-visibility: hidden;
	  -webkit-transition: -webkit-transform 0.3s;
			  transition: transform 0.3s;
	  background-position:center top; 
	  background-repeat:no-repeat; 
	  background-size:contain;
	}
	
	.card__front {
	 -webkit-transition: all 400ms ease; -moz-transition: all 400ms ease; -ms-transition: all 400ms ease; -o-transition: all 400ms ease; transition: all 400ms ease;
	}
	.card__front:hover {
		opacity:0.9;	
	}
	
	.card__back {
	
	  -webkit-transform: rotateY(-180deg);
			  transform: rotateY(-180deg);
	}
	
	/* card text */
	.card__text {
	  display: inline-block;
	  position: absolute;
	  top: 0;
	  right: 0;
	  bottom: 0;
	  left: 0;
	  margin: auto;
	}
	
	/* hover effect */
	.card.effect__hover:hover .card__front {
	  -webkit-transform: rotateY(-180deg);
			  transform: rotateY(-180deg);
	}
	
	.card.effect__hover:hover .card__back {
	  -webkit-transform: rotateY(0);
			  transform: rotateY(0);
	}
	
	/* click effect */
	.card.effect__click.flipped .card__front {
	  -webkit-transform: rotateY(-180deg);
			  transform: rotateY(-180deg);
	}
	
	.card.effect__click.flipped .card__back {
	  -webkit-transform: rotateY(0);
			  transform: rotateY(0);
	}
	
	/* random effect */
	.card.effect__random.flipped .card__front {
	  -webkit-transform: rotateY(-180deg);
			  transform: rotateY(-180deg);
	}
	
	.card.effect__random.flipped .card__back {
	  -webkit-transform: rotateY(0);
			  transform: rotateY(0);
	}

	
	.menu-icon {display:none;}

/* ==========================================================================
    Portrait iPad
   ========================================================================== */
	@media only screen and (max-width: 1023px){
		
		
		
	}

/* ==========================================================================
    Landscape iPhone
   ========================================================================== */
	@media only screen and (max-width: 900px){
		
		#header .wrapper {padding:0;}
		#logo {margin-left:20px;}
		.menu-icon {display:block; cursor:pointer; float:right; margin-top:30px; margin-right:20px;}
		.menu-icon-bar { display: block;width: 40px; height: 5px; background-color:#CE6323; margin-top:5px;}
		.menu-icon-bar:first-child {margin-top: 0px;}
		
		.nav{ display:none; height:auto; float:none; border-left:0; width:100%; clear:both; background-color:#fff;}
		.nav li{ display:block; height:auto; float:none; width:100%; text-align:right;}
			.nav li a{display:block; width:100%; height:auto; font-size:24px; line-height:24px;  color:#7d7d7d;  border-right:0; border-bottom:1px solid #eaeaea; padding:10px 20px 10px 20px;}
			.nav li.on a:after,
			.nav li a:hover:after {display:block; position:absolute; bottom:0px; left:0px; width:100%; content:" "; height:7px; background:#CE6323;}
		
		h1.page-title {font-size: 35px; line-height: 45px;}
		
	
		.section-text { padding-bottom: 20px;}
		.footer {font-size: 12px;line-height: 15px;}
		
	}

/* ==========================================================================
    Portrait iPhone
   ========================================================================== */
	@media only screen and (max-width: 479px){
		
		
		
	}