<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
         <rewrite>
             <rules>
                <rule name="Rewrite" stopProcessing="true">
                    <match url="(.+)" ignoreCase="false" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
                        <add input="{URL}" pattern="^/(favicon\.ico|apple-touch-icon.*\.png)$" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php?p={R:1}" appendQueryString="true" />
                </rule>
             </rules>
         </rewrite>
        <urlCompression doStaticCompression="true" doDynamicCompression="true" />
        <defaultDocument>
            <files>
                <clear />
                <add value="index.php" />
                <add value="index.htm" />
                <add value="index.html" />
            </files>
        </defaultDocument>
    </system.webServer>
</configuration>
