/*
 * Project Styles
 * 
 */

/* ==========================================================================
   Base styles and resets
   ========================================================================== */

html{color:#000;background:#FFF;}body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0;}table{border-collapse:collapse;border-spacing:0;}fieldset,img{border:0;}address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal;}li{list-style:none;}caption,th{text-align:left;}h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal;}q:before,q:after{content:'';}abbr,acronym{border:0;font-variant:normal;}sup{vertical-align:text-top;}sub{vertical-align:text-bottom;}input,textarea,select{font-family:inherit;font-size:inherit;font-weight:inherit;}input,textarea,select{*font-size:100%;}legend{color:#000;}
html{-webkit-text-size-adjust: none;}
strong{ font-weight:bold;}
img{ vertical-align:top;}
.cl{ clear:both; font-size:0;}

article, aside, details, figcaption, figure, footer, header, hgroup, nav, section { display: block; }
audio, canvas, video { display: inline-block; *display: inline; *zoom: 1; }
audio:not([controls]) { display: none; }
[hidden] { display: none; }

html { font-size: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
html, button, input, select, textarea { font-family: sans-serif; color: #222; }
body { margin: 0; font-size: 1em; line-height: 1.4; }

a { color: #00e; }
a:hover { color: #06e; }
a:focus { outline: thin dotted; }
a:hover, a:active { outline: 0; }
abbr[title] { border-bottom: 1px dotted; }
b, strong { font-weight: bold; }
blockquote { margin: 1em 40px; }
dfn { font-style: italic; }
hr { display: block; height: 1px; border: 0; border-top: 1px solid #ccc; margin: 1em 0; padding: 0; }
ins { background: #ff9; color: #000; text-decoration: none; }
mark { background: #ff0; color: #000; font-style: italic; font-weight: bold; }
pre, code, kbd, samp { font-family: monospace, serif; _font-family: 'courier new', monospace; font-size: 1em; }
pre { white-space: pre; white-space: pre-wrap; word-wrap: break-word; }

q { quotes: none; }
q:before, q:after { content: ""; content: none; }
small { font-size: 85%; }
sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-align: baseline; }
sup { top: -0.5em; }
sub { bottom: -0.25em; }

ul, ol { margin: 0; padding: 0; }
li { list-style:none; margin:0; padding:0; }
dd { margin: 0 0 0 40px; }
nav ul, nav ol { list-style: none; list-style-image: none; margin: 0; padding: 0; }

img { border: 0; -ms-interpolation-mode: bicubic; vertical-align: middle; }
svg:not(:root) { overflow: hidden; }
figure { margin: 0; }

form { margin: 0; }
fieldset { border: 0; margin: 0; padding: 0; }

label { cursor: pointer; }
legend { border: 0; *margin-left: -7px; padding: 0; white-space: normal; }
button, input, select, textarea { font-size: 100%; margin: 0; vertical-align: baseline; *vertical-align: middle; }
button, input { line-height: normal; }
button, input[type="button"], input[type="reset"], input[type="submit"] { cursor: pointer; -webkit-appearance: button; *overflow: visible; }
button[disabled], input[disabled] { cursor: default; }
input[type="checkbox"], input[type="radio"] { box-sizing: border-box; padding: 0; *width: 13px; *height: 13px; }
input[type="search"] { -webkit-appearance: textfield; -moz-box-sizing: content-box; -webkit-box-sizing: content-box; box-sizing: content-box; }
input[type="search"]::-webkit-search-decoration, input[type="search"]::-webkit-search-cancel-button { -webkit-appearance: none; }
button::-moz-focus-inner, input::-moz-focus-inner { border: 0; padding: 0; }
textarea { overflow: auto; vertical-align: top; resize: vertical; }
input:valid, textarea:valid {  }
input:invalid, textarea:invalid { background-color: #f0dddd; }

table { border-collapse: collapse; border-spacing: 0; }
td { vertical-align: top; }

.chromeframe { margin: 0.2em 0; background: #ccc; color: black; padding: 0.2em 0; }

.clearfix:before, .clearfix:after { content: ""; display: table; }
.clearfix:after { clear: both; }
.clearfix { *zoom: 1; }


/* ==========================================================================
   General styles
   ========================================================================== */
html { background:#fff; }
body { font-family:Arial; color:#000; }

.wrapper { max-width:810px; width:100%; margin:0 auto; padding:0px 20px; position:relative; }
.wrapper:before, .wrapper:after { content: ""; display: table; }
.wrapper:after { clear: both; }
.wrapper { *zoom: 1; }

a { text-decoration:none; -webkit-transition: all 400ms ease; -moz-transition: all 400ms ease; -ms-transition: all 400ms ease; -o-transition: all 400ms ease; transition: all 400ms ease;
outline:none!important; }
	a:hover { color:#fff;}

* { -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }

img {max-width:100%; height:auto;}

.col6 {
    width: 50%;
    float: left;
}


.content {padding:30px 0px;}

.image {margin-bottom:30px;}
.images {overflow:hidden; margin:0px -10px;}
	.images .image {width:50%; float:left; padding:0px 10px;}
	
	.hover .off{ display:none;}
	.hover:hover .on{ display:none;}
	.hover:hover .off{ display:block;}
	
.seperator {width:100%; clear:both; height:30px;}


/* Preloader + bar */
.loading {
	display: block;
	background-color: #faf9f6;
	top: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	text-align: center;
	position: fixed;
	z-index: 6000;
}
.loading .bar {
	background-color: #fff;
	width: 160px;
	color: #333;
	font-size: 20px;
	height: 10px;
	text-align: center;
	margin-left: 20px;
	margin-top: 20px;
}
.loading .percent { /* Preload percentage bar */
	background-color: #F18D00;
	width: 2%;
	height: 10px;
}
.logo-preload {
	width: 150px;
	height: 220px;
	margin-left: 50px;
	background-image: url(../images/Logo.png);
	background-repeat: no-repeat;
	background-position: center top;
}
.loadingframe {
	width: 150px;
	height: 280px;
	margin: 0 auto;
	top: 50%;
	margin-top: -140px;
	left: 50%;
	margin-left: -150px;
	position: absolute;
}
.animated {
	-webkit-animation-duration: 1s;
	animation-duration: 1s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	-webkit-transition: width 0.3s ease, height 0.3s ease;
	-moz-transition: width 0.3s ease, height 0.3s ease;
	-o-transition: width 0.3s ease, height 0.3s ease;
	-ms-transition: width 0.3s ease, height 0.3s ease;
	transition: width 0.3s ease, height 0.3s ease;
}
 @-webkit-keyframes fadeInUp {
0% {
opacity:0;
-webkit-transform:translateY(0px);
transform:translateY(0px)
}
100% {
opacity:1;
-webkit-transform:translateY(0);
transform:translateY(0)
}
}
@keyframes fadeInUp {
0% {
opacity:0;
-webkit-transform:translateY(0px);
-ms-transform:translateY(0px);
transform:translateY(0px)
}
100% {
opacity:1;
-webkit-transform:translateY(0);
-ms-transform:translateY(0);
transform:translateY(0)
}
}
.fadeInUp {
	-webkit-animation-name: fadeInUp;
	animation-name: fadeInUp
}