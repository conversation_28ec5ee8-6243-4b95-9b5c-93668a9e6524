/* Rotation */
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%; }

body {
  margin: 0; }

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block; }

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline; }

audio:not([controls]) {
  display: none;
  height: 0; }

[hidden],
template {
  display: none; }

a {
  background-color: transparent; }

a:active,
a:hover {
  outline: 0; }

abbr[title] {
  border-bottom: 1px dotted; }

b,
strong {
  font-weight: bold; }

dfn {
  font-style: italic; }

h1 {
  font-size: 2em;
  margin: 0.67em 0; }

mark {
  background: #ff0;
  color: #000; }

small {
  font-size: 80%; }

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sup {
  top: -0.5em; }

sub {
  bottom: -0.25em; }

img {
  border: 0; }

svg:not(:root) {
  overflow: hidden; }

figure {
  margin: 1em 40px; }

hr {
  box-sizing: content-box;
  height: 0; }

pre {
  overflow: auto; }

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em; }

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0; }

button {
  overflow: visible; }

button,
select {
  text-transform: none; }

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer; }

button[disabled],
html input[disabled] {
  cursor: default; }

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0; }

input {
  line-height: normal; }

input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0; }

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto; }

input[type="search"] {
  -webkit-appearance: textfield;
  box-sizing: content-box; }

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em; }

legend {
  border: 0;
  padding: 0; }

textarea {
  overflow: auto; }

optgroup {
  font-weight: bold; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

td,
th {
  padding: 0; }

.clearfix:before, .clearfix:after {
  content: " ";
  display: table; }

.clearfix:after {
  clear: both; }

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto; }

.pull-right {
  float: right !important; }

.pull-left {
  float: left !important; }

.hide {
  display: none !important; }

.show {
  display: block !important; }

.invisible {
  visibility: hidden; }

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0; }

.hidden {
  display: none !important; }

.affix {
  position: fixed; }

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit; }
  h1 small,
  h1 .small, h2 small,
  h2 .small, h3 small,
  h3 .small, h4 small,
  h4 .small, h5 small,
  h5 .small, h6 small,
  h6 .small,
  .h1 small,
  .h1 .small, .h2 small,
  .h2 .small, .h3 small,
  .h3 .small, .h4 small,
  .h4 .small, .h5 small,
  .h5 .small, .h6 small,
  .h6 .small {
    font-weight: normal;
    line-height: 1;
    color: #777777; }

h1, .h1,
h2, .h2,
h3, .h3 {
  margin-top: 20px;
  margin-bottom: 10px; }
  h1 small,
  h1 .small, .h1 small,
  .h1 .small,
  h2 small,
  h2 .small, .h2 small,
  .h2 .small,
  h3 small,
  h3 .small, .h3 small,
  .h3 .small {
    font-size: 65%; }

h4, .h4,
h5, .h5,
h6, .h6 {
  margin-top: 10px;
  margin-bottom: 10px; }
  h4 small,
  h4 .small, .h4 small,
  .h4 .small,
  h5 small,
  h5 .small, .h5 small,
  .h5 .small,
  h6 small,
  h6 .small, .h6 small,
  .h6 .small {
    font-size: 75%; }

h1, .h1 {
  font-size: 36px; }

h2, .h2 {
  font-size: 30px; }

h3, .h3 {
  font-size: 24px; }

h4, .h4 {
  font-size: 18px; }

h5, .h5 {
  font-size: 14px; }

h6, .h6 {
  font-size: 12px; }

p {
  margin: 0 0 10px; }

.lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.4; }
  @media (min-width: 768px) {
    .lead {
      font-size: 21px; } }

small,
.small {
  font-size: 85%; }

mark,
.mark {
  background-color: #fcf8e3;
  padding: .2em; }

.text-left {
  text-align: left; }

.text-right {
  text-align: right; }

.text-center {
  text-align: center; }

.text-justify {
  text-align: justify; }

.text-nowrap {
  white-space: nowrap; }

.text-lowercase {
  text-transform: lowercase; }

.text-uppercase, .initialism {
  text-transform: uppercase; }

.text-capitalize {
  text-transform: capitalize; }

.text-muted {
  color: #999999; }

.text-primary {
  color: #216a95; }

a.text-primary:hover,
a.text-primary:focus {
  color: #184c6b; }

.text-success {
  color: #3c763d; }

a.text-success:hover,
a.text-success:focus {
  color: #2b542c; }

.text-info {
  color: #397fa3; }

a.text-info:hover,
a.text-info:focus {
  color: #2c627d; }

.text-warning {
  color: #8a6d3b; }

a.text-warning:hover,
a.text-warning:focus {
  color: #66512c; }

.text-danger {
  color: #a94442; }

a.text-danger:hover,
a.text-danger:focus {
  color: #843534; }

.bg-primary {
  color: #fff; }

.bg-primary {
  background-color: #216a95; }

a.bg-primary:hover,
a.bg-primary:focus {
  background-color: #184c6b; }

.bg-success {
  background-color: #dff0d8; }

a.bg-success:hover,
a.bg-success:focus {
  background-color: #c1e2b3; }

.bg-info {
  background-color: #d9edf7; }

a.bg-info:hover,
a.bg-info:focus {
  background-color: #afd9ee; }

.bg-warning {
  background-color: #fcf8e3; }

a.bg-warning:hover,
a.bg-warning:focus {
  background-color: #f7ecb5; }

.bg-danger {
  background-color: #f2dede; }

a.bg-danger:hover,
a.bg-danger:focus {
  background-color: #e4b9b9; }

.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eeeeee; }

ul,
ol {
  margin-top: 0;
  margin-bottom: 10px; }
  ul ul,
  ul ol,
  ol ul,
  ol ol {
    margin-bottom: 0; }

.list-unstyled {
  padding-left: 0;
  list-style: none; }

.list-inline {
  padding-left: 0;
  list-style: none;
  margin-left: -5px; }
  .list-inline > li {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px; }

dl {
  margin-top: 0;
  margin-bottom: 20px; }

dt,
dd {
  line-height: 1.428571429; }

dt {
  font-weight: bold; }

dd {
  margin-left: 0; }

.dl-horizontal dd:before, .dl-horizontal dd:after {
  content: " ";
  display: table; }

.dl-horizontal dd:after {
  clear: both; }

@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
  .dl-horizontal dd {
    margin-left: 180px; } }

abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #777777; }

.initialism {
  font-size: 90%; }

blockquote {
  padding: 10px 20px;
  margin: 0 0 20px;
  font-size: 17.5px;
  border-left: 5px solid #eeeeee; }
  blockquote p:last-child,
  blockquote ul:last-child,
  blockquote ol:last-child {
    margin-bottom: 0; }
  blockquote footer,
  blockquote small,
  blockquote .small {
    display: block;
    font-size: 80%;
    line-height: 1.428571429;
    color: #777777; }
    blockquote footer:before,
    blockquote small:before,
    blockquote .small:before {
      content: '\2014 \00A0'; }

.blockquote-reverse,
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eeeeee;
  border-left: 0;
  text-align: right; }
  .blockquote-reverse footer:before,
  .blockquote-reverse small:before,
  .blockquote-reverse .small:before,
  blockquote.pull-right footer:before,
  blockquote.pull-right small:before,
  blockquote.pull-right .small:before {
    content: ''; }
  .blockquote-reverse footer:after,
  .blockquote-reverse small:after,
  .blockquote-reverse .small:after,
  blockquote.pull-right footer:after,
  blockquote.pull-right small:after,
  blockquote.pull-right .small:after {
    content: '\00A0 \2014'; }

address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.428571429; }

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

html {
  font-size: 10px;
  -webkit-tap-highlight-color: transparent; }

body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.428571429;
  color: #ffffff;
  background-color: #fff; }

input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit; }

a {
  color: #216a95;
  text-decoration: none; }
  a:hover, a:focus {
    color: #216a95;
    text-decoration: underline; }
  a:focus {
    outline: none;
    outline: none;
    outline-offset: 0; }

figure {
  margin: 0; }

img {
  vertical-align: middle; }

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto; }

.img-rounded {
  border-radius: 6px; }

.img-thumbnail {
  padding: 4px;
  line-height: 1.428571429;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto; }

.img-circle {
  border-radius: 50%; }

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eeeeee; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto; }

[role="button"] {
  cursor: pointer; }

.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: bold;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }
  .btn:focus, .btn.focus, .btn:active:focus, .btn:active.focus, .btn.active:focus, .btn.active.focus {
    outline: none;
    outline: none;
    outline-offset: 0; }
  .btn:hover, .btn:focus, .btn.focus {
    color: #777777;
    text-decoration: none; }
  .btn:active, .btn.active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }
  .btn.disabled, .btn[disabled],
  fieldset[disabled] .btn {
    cursor: not-allowed;
    opacity: 0.65;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none; }

a.btn.disabled,
fieldset[disabled] a.btn {
  pointer-events: none; }

.btn-default {
  text-transform: uppercase;
  color: #777777;
  background-color: #eeeeee;
  border-color: #e7e7e7; }
  .btn-default:hover, .btn-default:focus, .btn-default:active, .btn-default.active {
    color: #777777;
    background-color: #e7e7e7;
    border-color: #e7e7e7; }
  .open .btn-default.dropdown-toggle {
    color: #777777;
    background-color: #e7e7e7;
    border-color: #e7e7e7; }
  .btn-default:active, .btn-default.active {
    background-image: none; }
  .open .btn-default.dropdown-toggle {
    background-image: none; }
  .btn-default.disabled, .btn-default.disabled:hover, .btn-default.disabled:focus, .btn-default.disabled:active, .btn-default.disabled.active, .btn-default[disabled], .btn-default[disabled]:hover, .btn-default[disabled]:focus, .btn-default[disabled]:active, .btn-default[disabled].active,
  fieldset[disabled] .btn-default,
  fieldset[disabled] .btn-default:hover,
  fieldset[disabled] .btn-default:focus,
  fieldset[disabled] .btn-default:active,
  fieldset[disabled] .btn-default.active {
    background-color: #eeeeee;
    border-color: #e7e7e7; }
  .btn-default.btn-flat {
    border-color: #eeeeee; }

.btn-primary {
  text-transform: uppercase;
  color: #ffffff;
  background-color: #DD730C;
  border-color: #DD730C; }
  .btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active {
    color: #ffffff;
    background-color: #DD730C;
    border-color: #DD730C; }
  .open .btn-primary.dropdown-toggle {
    color: #ffffff;
    background-color: #DD730C;
    border-color: #DD730C; }
  .btn-primary:active, .btn-primary.active {
    background-image: none; }
  .open .btn-primary.dropdown-toggle {
    background-image: none; }
  .btn-primary.disabled, .btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled:active, .btn-primary.disabled.active, .btn-primary[disabled], .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled]:active, .btn-primary[disabled].active,
  fieldset[disabled] .btn-primary,
  fieldset[disabled] .btn-primary:hover,
  fieldset[disabled] .btn-primary:focus,
  fieldset[disabled] .btn-primary:active,
  fieldset[disabled] .btn-primary.active {
    background-color: #DD730C;
    border-color: #DD730C; }
  .btn-primary.btn-flat {
    border-color: #DD730C; }

.btn-success {
  text-transform: uppercase;
  color: #fff;
  background-color: #5CA800;
  border-color: #5ba600; }
  .btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active {
    color: #fff;
    background-color: #549900;
    border-color: #5ba600; }
  .open .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #549900;
    border-color: #5ba600; }
  .btn-success:active, .btn-success.active {
    background-image: none; }
  .open .btn-success.dropdown-toggle {
    background-image: none; }
  .btn-success.disabled, .btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled:active, .btn-success.disabled.active, .btn-success[disabled], .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled]:active, .btn-success[disabled].active,
  fieldset[disabled] .btn-success,
  fieldset[disabled] .btn-success:hover,
  fieldset[disabled] .btn-success:focus,
  fieldset[disabled] .btn-success:active,
  fieldset[disabled] .btn-success.active {
    background-color: #5CA800;
    border-color: #5ba600; }
  .btn-success.btn-flat {
    border-color: #5CA800; }

.btn-info {
  text-transform: uppercase;
  color: #ffffff;
  background-color: #266B93;
  border-color: #25688f; }
  .btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active {
    color: #ffffff;
    background-color: #236287;
    border-color: #25688f; }
  .open .btn-info.dropdown-toggle {
    color: #ffffff;
    background-color: #236287;
    border-color: #25688f; }
  .btn-info:active, .btn-info.active {
    background-image: none; }
  .open .btn-info.dropdown-toggle {
    background-image: none; }
  .btn-info.disabled, .btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled:active, .btn-info.disabled.active, .btn-info[disabled], .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled]:active, .btn-info[disabled].active,
  fieldset[disabled] .btn-info,
  fieldset[disabled] .btn-info:hover,
  fieldset[disabled] .btn-info:focus,
  fieldset[disabled] .btn-info:active,
  fieldset[disabled] .btn-info.active {
    background-color: #266B93;
    border-color: #25688f; }
  .btn-info.btn-flat {
    border-color: #266B93; }

.btn-warning {
  text-transform: uppercase;
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236; }
  .btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active {
    color: #fff;
    background-color: #efa640;
    border-color: #eea236; }
  .open .btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #efa640;
    border-color: #eea236; }
  .btn-warning:active, .btn-warning.active {
    background-image: none; }
  .open .btn-warning.dropdown-toggle {
    background-image: none; }
  .btn-warning.disabled, .btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled:active, .btn-warning.disabled.active, .btn-warning[disabled], .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled]:active, .btn-warning[disabled].active,
  fieldset[disabled] .btn-warning,
  fieldset[disabled] .btn-warning:hover,
  fieldset[disabled] .btn-warning:focus,
  fieldset[disabled] .btn-warning:active,
  fieldset[disabled] .btn-warning.active {
    background-color: #f0ad4e;
    border-color: #eea236; }
  .btn-warning.btn-flat {
    border-color: #f0ad4e; }

.btn-danger {
  text-transform: uppercase;
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a; }
  .btn-danger:hover, .btn-danger:focus, .btn-danger:active, .btn-danger.active {
    color: #fff;
    background-color: #d64742;
    border-color: #d43f3a; }
  .open .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #d64742;
    border-color: #d43f3a; }
  .btn-danger:active, .btn-danger.active {
    background-image: none; }
  .open .btn-danger.dropdown-toggle {
    background-image: none; }
  .btn-danger.disabled, .btn-danger.disabled:hover, .btn-danger.disabled:focus, .btn-danger.disabled:active, .btn-danger.disabled.active, .btn-danger[disabled], .btn-danger[disabled]:hover, .btn-danger[disabled]:focus, .btn-danger[disabled]:active, .btn-danger[disabled].active,
  fieldset[disabled] .btn-danger,
  fieldset[disabled] .btn-danger:hover,
  fieldset[disabled] .btn-danger:focus,
  fieldset[disabled] .btn-danger:active,
  fieldset[disabled] .btn-danger.active {
    background-color: #d9534f;
    border-color: #d43f3a; }
  .btn-danger.btn-flat {
    border-color: #d9534f; }

.btn-link {
  color: #216a95;
  font-weight: normal;
  border-radius: 0; }
  .btn-link, .btn-link:active, .btn-link.active, .btn-link[disabled],
  fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none; }
  .btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active {
    border-color: transparent; }
  .btn-link:hover, .btn-link:focus {
    color: #216a95;
    text-decoration: underline;
    background-color: transparent; }
  .btn-link[disabled]:hover, .btn-link[disabled]:focus,
  fieldset[disabled] .btn-link:hover,
  fieldset[disabled] .btn-link:focus {
    color: #777777;
    text-decoration: none; }

.btn-lg {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px; }

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }

.btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }

.btn-block {
  display: block;
  width: 100%; }

.btn-block + .btn-block {
  margin-top: 5px; }

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%; }

.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 0px;
  padding-right: 0px; }
  .container:before, .container:after {
    content: " ";
    display: table; }
  .container:after {
    clear: both; }
  @media (min-width: 768px) {
    .container {
      width: 750px; } }
  @media (min-width: 992px) {
    .container {
      width: 970px; } }
  @media (min-width: 1200px) {
    .container {
      width: 1170px; } }

.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 0px;
  padding-right: 0px; }
  .container-fluid:before, .container-fluid:after {
    content: " ";
    display: table; }
  .container-fluid:after {
    clear: both; }

.row {
  margin-left: 0px;
  margin-right: 0px; }
  .row:before, .row:after {
    content: " ";
    display: table; }
  .row:after {
    clear: both; }

.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 0px;
  padding-right: 0px; }

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
  float: left; }

.col-xs-1 {
  width: 8.3333333333%; }

.col-xs-2 {
  width: 16.6666666667%; }

.col-xs-3 {
  width: 25%; }

.col-xs-4 {
  width: 33.3333333333%; }

.col-xs-5 {
  width: 41.6666666667%; }

.col-xs-6 {
  width: 50%; }

.col-xs-7 {
  width: 58.3333333333%; }

.col-xs-8 {
  width: 66.6666666667%; }

.col-xs-9 {
  width: 75%; }

.col-xs-10 {
  width: 83.3333333333%; }

.col-xs-11 {
  width: 91.6666666667%; }

.col-xs-12 {
  width: 100%; }

.col-xs-pull-0 {
  right: auto; }

.col-xs-pull-1 {
  right: 8.3333333333%; }

.col-xs-pull-2 {
  right: 16.6666666667%; }

.col-xs-pull-3 {
  right: 25%; }

.col-xs-pull-4 {
  right: 33.3333333333%; }

.col-xs-pull-5 {
  right: 41.6666666667%; }

.col-xs-pull-6 {
  right: 50%; }

.col-xs-pull-7 {
  right: 58.3333333333%; }

.col-xs-pull-8 {
  right: 66.6666666667%; }

.col-xs-pull-9 {
  right: 75%; }

.col-xs-pull-10 {
  right: 83.3333333333%; }

.col-xs-pull-11 {
  right: 91.6666666667%; }

.col-xs-pull-12 {
  right: 100%; }

.col-xs-push-0 {
  left: auto; }

.col-xs-push-1 {
  left: 8.3333333333%; }

.col-xs-push-2 {
  left: 16.6666666667%; }

.col-xs-push-3 {
  left: 25%; }

.col-xs-push-4 {
  left: 33.3333333333%; }

.col-xs-push-5 {
  left: 41.6666666667%; }

.col-xs-push-6 {
  left: 50%; }

.col-xs-push-7 {
  left: 58.3333333333%; }

.col-xs-push-8 {
  left: 66.6666666667%; }

.col-xs-push-9 {
  left: 75%; }

.col-xs-push-10 {
  left: 83.3333333333%; }

.col-xs-push-11 {
  left: 91.6666666667%; }

.col-xs-push-12 {
  left: 100%; }

.col-xs-offset-0 {
  margin-left: 0%; }

.col-xs-offset-1 {
  margin-left: 8.3333333333%; }

.col-xs-offset-2 {
  margin-left: 16.6666666667%; }

.col-xs-offset-3 {
  margin-left: 25%; }

.col-xs-offset-4 {
  margin-left: 33.3333333333%; }

.col-xs-offset-5 {
  margin-left: 41.6666666667%; }

.col-xs-offset-6 {
  margin-left: 50%; }

.col-xs-offset-7 {
  margin-left: 58.3333333333%; }

.col-xs-offset-8 {
  margin-left: 66.6666666667%; }

.col-xs-offset-9 {
  margin-left: 75%; }

.col-xs-offset-10 {
  margin-left: 83.3333333333%; }

.col-xs-offset-11 {
  margin-left: 91.6666666667%; }

.col-xs-offset-12 {
  margin-left: 100%; }

@media (min-width: 768px) {
  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
    float: left; }
  .col-sm-1 {
    width: 8.3333333333%; }
  .col-sm-2 {
    width: 16.6666666667%; }
  .col-sm-3 {
    width: 25%; }
  .col-sm-4 {
    width: 33.3333333333%; }
  .col-sm-5 {
    width: 41.6666666667%; }
  .col-sm-6 {
    width: 50%; }
  .col-sm-7 {
    width: 58.3333333333%; }
  .col-sm-8 {
    width: 66.6666666667%; }
  .col-sm-9 {
    width: 75%; }
  .col-sm-10 {
    width: 83.3333333333%; }
  .col-sm-11 {
    width: 91.6666666667%; }
  .col-sm-12 {
    width: 100%; }
  .col-sm-pull-0 {
    right: auto; }
  .col-sm-pull-1 {
    right: 8.3333333333%; }
  .col-sm-pull-2 {
    right: 16.6666666667%; }
  .col-sm-pull-3 {
    right: 25%; }
  .col-sm-pull-4 {
    right: 33.3333333333%; }
  .col-sm-pull-5 {
    right: 41.6666666667%; }
  .col-sm-pull-6 {
    right: 50%; }
  .col-sm-pull-7 {
    right: 58.3333333333%; }
  .col-sm-pull-8 {
    right: 66.6666666667%; }
  .col-sm-pull-9 {
    right: 75%; }
  .col-sm-pull-10 {
    right: 83.3333333333%; }
  .col-sm-pull-11 {
    right: 91.6666666667%; }
  .col-sm-pull-12 {
    right: 100%; }
  .col-sm-push-0 {
    left: auto; }
  .col-sm-push-1 {
    left: 8.3333333333%; }
  .col-sm-push-2 {
    left: 16.6666666667%; }
  .col-sm-push-3 {
    left: 25%; }
  .col-sm-push-4 {
    left: 33.3333333333%; }
  .col-sm-push-5 {
    left: 41.6666666667%; }
  .col-sm-push-6 {
    left: 50%; }
  .col-sm-push-7 {
    left: 58.3333333333%; }
  .col-sm-push-8 {
    left: 66.6666666667%; }
  .col-sm-push-9 {
    left: 75%; }
  .col-sm-push-10 {
    left: 83.3333333333%; }
  .col-sm-push-11 {
    left: 91.6666666667%; }
  .col-sm-push-12 {
    left: 100%; }
  .col-sm-offset-0 {
    margin-left: 0%; }
  .col-sm-offset-1 {
    margin-left: 8.3333333333%; }
  .col-sm-offset-2 {
    margin-left: 16.6666666667%; }
  .col-sm-offset-3 {
    margin-left: 25%; }
  .col-sm-offset-4 {
    margin-left: 33.3333333333%; }
  .col-sm-offset-5 {
    margin-left: 41.6666666667%; }
  .col-sm-offset-6 {
    margin-left: 50%; }
  .col-sm-offset-7 {
    margin-left: 58.3333333333%; }
  .col-sm-offset-8 {
    margin-left: 66.6666666667%; }
  .col-sm-offset-9 {
    margin-left: 75%; }
  .col-sm-offset-10 {
    margin-left: 83.3333333333%; }
  .col-sm-offset-11 {
    margin-left: 91.6666666667%; }
  .col-sm-offset-12 {
    margin-left: 100%; } }

@media (min-width: 992px) {
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: left; }
  .col-md-1 {
    width: 8.3333333333%; }
  .col-md-2 {
    width: 16.6666666667%; }
  .col-md-3 {
    width: 25%; }
  .col-md-4 {
    width: 33.3333333333%; }
  .col-md-5 {
    width: 41.6666666667%; }
  .col-md-6 {
    width: 50%; }
  .col-md-7 {
    width: 58.3333333333%; }
  .col-md-8 {
    width: 66.6666666667%; }
  .col-md-9 {
    width: 75%; }
  .col-md-10 {
    width: 83.3333333333%; }
  .col-md-11 {
    width: 91.6666666667%; }
  .col-md-12 {
    width: 100%; }
  .col-md-pull-0 {
    right: auto; }
  .col-md-pull-1 {
    right: 8.3333333333%; }
  .col-md-pull-2 {
    right: 16.6666666667%; }
  .col-md-pull-3 {
    right: 25%; }
  .col-md-pull-4 {
    right: 33.3333333333%; }
  .col-md-pull-5 {
    right: 41.6666666667%; }
  .col-md-pull-6 {
    right: 50%; }
  .col-md-pull-7 {
    right: 58.3333333333%; }
  .col-md-pull-8 {
    right: 66.6666666667%; }
  .col-md-pull-9 {
    right: 75%; }
  .col-md-pull-10 {
    right: 83.3333333333%; }
  .col-md-pull-11 {
    right: 91.6666666667%; }
  .col-md-pull-12 {
    right: 100%; }
  .col-md-push-0 {
    left: auto; }
  .col-md-push-1 {
    left: 8.3333333333%; }
  .col-md-push-2 {
    left: 16.6666666667%; }
  .col-md-push-3 {
    left: 25%; }
  .col-md-push-4 {
    left: 33.3333333333%; }
  .col-md-push-5 {
    left: 41.6666666667%; }
  .col-md-push-6 {
    left: 50%; }
  .col-md-push-7 {
    left: 58.3333333333%; }
  .col-md-push-8 {
    left: 66.6666666667%; }
  .col-md-push-9 {
    left: 75%; }
  .col-md-push-10 {
    left: 83.3333333333%; }
  .col-md-push-11 {
    left: 91.6666666667%; }
  .col-md-push-12 {
    left: 100%; }
  .col-md-offset-0 {
    margin-left: 0%; }
  .col-md-offset-1 {
    margin-left: 8.3333333333%; }
  .col-md-offset-2 {
    margin-left: 16.6666666667%; }
  .col-md-offset-3 {
    margin-left: 25%; }
  .col-md-offset-4 {
    margin-left: 33.3333333333%; }
  .col-md-offset-5 {
    margin-left: 41.6666666667%; }
  .col-md-offset-6 {
    margin-left: 50%; }
  .col-md-offset-7 {
    margin-left: 58.3333333333%; }
  .col-md-offset-8 {
    margin-left: 66.6666666667%; }
  .col-md-offset-9 {
    margin-left: 75%; }
  .col-md-offset-10 {
    margin-left: 83.3333333333%; }
  .col-md-offset-11 {
    margin-left: 91.6666666667%; }
  .col-md-offset-12 {
    margin-left: 100%; } }

@media (min-width: 1200px) {
  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
    float: left; }
  .col-lg-1 {
    width: 8.3333333333%; }
  .col-lg-2 {
    width: 16.6666666667%; }
  .col-lg-3 {
    width: 25%; }
  .col-lg-4 {
    width: 33.3333333333%; }
  .col-lg-5 {
    width: 41.6666666667%; }
  .col-lg-6 {
    width: 50%; }
  .col-lg-7 {
    width: 58.3333333333%; }
  .col-lg-8 {
    width: 66.6666666667%; }
  .col-lg-9 {
    width: 75%; }
  .col-lg-10 {
    width: 83.3333333333%; }
  .col-lg-11 {
    width: 91.6666666667%; }
  .col-lg-12 {
    width: 100%; }
  .col-lg-pull-0 {
    right: auto; }
  .col-lg-pull-1 {
    right: 8.3333333333%; }
  .col-lg-pull-2 {
    right: 16.6666666667%; }
  .col-lg-pull-3 {
    right: 25%; }
  .col-lg-pull-4 {
    right: 33.3333333333%; }
  .col-lg-pull-5 {
    right: 41.6666666667%; }
  .col-lg-pull-6 {
    right: 50%; }
  .col-lg-pull-7 {
    right: 58.3333333333%; }
  .col-lg-pull-8 {
    right: 66.6666666667%; }
  .col-lg-pull-9 {
    right: 75%; }
  .col-lg-pull-10 {
    right: 83.3333333333%; }
  .col-lg-pull-11 {
    right: 91.6666666667%; }
  .col-lg-pull-12 {
    right: 100%; }
  .col-lg-push-0 {
    left: auto; }
  .col-lg-push-1 {
    left: 8.3333333333%; }
  .col-lg-push-2 {
    left: 16.6666666667%; }
  .col-lg-push-3 {
    left: 25%; }
  .col-lg-push-4 {
    left: 33.3333333333%; }
  .col-lg-push-5 {
    left: 41.6666666667%; }
  .col-lg-push-6 {
    left: 50%; }
  .col-lg-push-7 {
    left: 58.3333333333%; }
  .col-lg-push-8 {
    left: 66.6666666667%; }
  .col-lg-push-9 {
    left: 75%; }
  .col-lg-push-10 {
    left: 83.3333333333%; }
  .col-lg-push-11 {
    left: 91.6666666667%; }
  .col-lg-push-12 {
    left: 100%; }
  .col-lg-offset-0 {
    margin-left: 0%; }
  .col-lg-offset-1 {
    margin-left: 8.3333333333%; }
  .col-lg-offset-2 {
    margin-left: 16.6666666667%; }
  .col-lg-offset-3 {
    margin-left: 25%; }
  .col-lg-offset-4 {
    margin-left: 33.3333333333%; }
  .col-lg-offset-5 {
    margin-left: 41.6666666667%; }
  .col-lg-offset-6 {
    margin-left: 50%; }
  .col-lg-offset-7 {
    margin-left: 58.3333333333%; }
  .col-lg-offset-8 {
    margin-left: 66.6666666667%; }
  .col-lg-offset-9 {
    margin-left: 75%; }
  .col-lg-offset-10 {
    margin-left: 83.3333333333%; }
  .col-lg-offset-11 {
    margin-left: 91.6666666667%; }
  .col-lg-offset-12 {
    margin-left: 100%; } }

.breadcrumb-immobiliare {
  background-color: #ffffff;
  font-size: 14px;
  display: inline-block;
  border-radius: 4px;
  margin-bottom: 0;
  padding: 10px 10px 10px 0px;
  vertical-align: middle;
  line-height: 19px; }
  .breadcrumb-immobiliare > li {
    position: relative;
    display: inline-block; }
    .breadcrumb-immobiliare > li + li:before {
      -webkit-transform: rotate(45deg);
      -moz-transform: rotate(45deg);
      -o-transform: rotate(45deg);
      transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      position: relative;
      content: "";
      padding: 0;
      height: 8px;
      width: 8px;
      background-color: #4F9AC0;
      display: inline-block;
      border-radius: 1px;
      margin: 0 8px 1px 4px; }
      .lt-ie9 .breadcrumb-immobiliare > li + li:before {
        filter: "progid:DXImageTransform.Microsoft.Matrix(sizingMethod=" auto expand ", M11=0.3239570563, M12=-0.7071067812, M21=0.7071067812, M22=0.3239570563)";
        -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(sizingMethod='auto expand', M11=0.3239570563, M12=-0.7071067812, M21=0.7071067812, M22=0.3239570563)";
        zoom: 1; }
    .breadcrumb-immobiliare > li + li:after {
      content: '';
      padding: 0;
      height: 14px;
      width: 7px;
      background-color: #ffffff;
      position: absolute;
      top: 4px;
      left: 0;
      z-index: 2; }
  .breadcrumb-immobiliare a {
    color: #216a95; }
  .breadcrumb-immobiliare > .active {
    color: #216a95; }

.lt-ie9 .breadcrumb-immobiliare > li + li:before {
  height: 0;
  width: 0;
  background-color: transparent;
  border: 5px solid transparent;
  border-left-color: #4F9AC0;
  margin: 0 3px;
  z-index: 3; }

.btn-white {
  text-transform: uppercase;
  color: #767676;
  background-color: #ffffff;
  border-color: #ccc;
  text-transform: uppercase; }
  .btn-white:hover, .btn-white:focus, .btn-white:active, .btn-white.active {
    color: #767676;
    background-color: #f7f7f7;
    border-color: #cccccc; }
  .open .btn-white.dropdown-toggle {
    color: #767676;
    background-color: #f7f7f7;
    border-color: #cccccc; }
  .btn-white:active, .btn-white.active {
    background-image: none; }
  .open .btn-white.dropdown-toggle {
    background-image: none; }
  .btn-white.disabled, .btn-white.disabled:hover, .btn-white.disabled:focus, .btn-white.disabled:active, .btn-white.disabled.active, .btn-white[disabled], .btn-white[disabled]:hover, .btn-white[disabled]:focus, .btn-white[disabled]:active, .btn-white[disabled].active,
  fieldset[disabled] .btn-white,
  fieldset[disabled] .btn-white:hover,
  fieldset[disabled] .btn-white:focus,
  fieldset[disabled] .btn-white:active,
  fieldset[disabled] .btn-white.active {
    background-color: #ffffff;
    border-color: #ccc; }
  .btn-white.btn-flat {
    border-color: #ffffff; }

.btn-ghost-gray {
  text-transform: uppercase;
  color: #767676;
  background-color: #ffffff;
  border-color: #888;
  background: transparent;
  border-width: 2px;
  text-transform: uppercase; }
  .btn-ghost-gray:hover, .btn-ghost-gray:focus, .btn-ghost-gray:active, .btn-ghost-gray.active {
    color: #767676;
    background-color: #f7f7f7;
    border-color: #888888; }
  .open .btn-ghost-gray.dropdown-toggle {
    color: #767676;
    background-color: #f7f7f7;
    border-color: #888888; }
  .btn-ghost-gray:active, .btn-ghost-gray.active {
    background-image: none; }
  .open .btn-ghost-gray.dropdown-toggle {
    background-image: none; }
  .btn-ghost-gray.disabled, .btn-ghost-gray.disabled:hover, .btn-ghost-gray.disabled:focus, .btn-ghost-gray.disabled:active, .btn-ghost-gray.disabled.active, .btn-ghost-gray[disabled], .btn-ghost-gray[disabled]:hover, .btn-ghost-gray[disabled]:focus, .btn-ghost-gray[disabled]:active, .btn-ghost-gray[disabled].active,
  fieldset[disabled] .btn-ghost-gray,
  fieldset[disabled] .btn-ghost-gray:hover,
  fieldset[disabled] .btn-ghost-gray:focus,
  fieldset[disabled] .btn-ghost-gray:active,
  fieldset[disabled] .btn-ghost-gray.active {
    background-color: #ffffff;
    border-color: #888; }
  .btn-ghost-gray.btn-flat {
    border-color: #ffffff; }

.btn {
  padding: 8px 16px;
  font-size: 13px;
  line-height: 19px;
  border-radius: 3px;
  font-weight: bold;
  font-size: 14px\0; }

.btn-xl {
  padding: 1em 18px;
  font-size: 17px;
  line-height: 1.5em;
  border-radius: 3px;
  font-size: 18px\0; }

.btn-lg {
  padding: 11px 16px;
  font-size: 13px;
  line-height: 1.3333333;
  border-radius: 3px;
  font-size: 14px\0; }

.btn-sm {
  padding: 6px 14px;
  font-size: 13px;
  line-height: 1.3333333;
  border-radius: 3px;
  font-size: 14px\0; }

.btn-xs {
  padding: 2px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
  font-size: 13px\0; }

.btn-link {
  font-size: 14px; }

.nav-footer:before, .nav-footer:after {
  content: " ";
  display: table; }

.nav-footer:after {
  clear: both; }

.nav-footer > li > a {
  display: inline-block;
  padding: 3px 0;
  color: #71d0ff; }
  .nav-footer > li > a:hover, .nav-footer > li > a:focus {
    background: none;
    text-decoration: underline; }

.nav-footer .caret {
  margin-left: 4px; }

.nav-footer .dropdown-menu li > a {
  padding-left: 12px; }

.nav-footer a .caret, .nav-footer a:hover .caret {
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px solid #ffffff;
  border-right: 0;
  margin-bottom: 1px; }

.nav-footer .open > a, .nav-footer .open > a:hover, .nav-footer .open > a:focus {
  background: none; }

.search-form-tabs {
  float: right;
  border-bottom: 0; }
  .search-form-tabs > li > a {
    height: 45px;
    margin-left: 5px;
    margin-right: 0;
    padding: 8px 20px 8px 15px;
    line-height: 1.428571429;
    background: #266B93;
    border: 1px solid #266B93;
    border-radius: 2px 2px 0 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale; }
    .search-form-tabs > li > a:hover {
      border-color: #2b6482 #2b6482 #2b6482;
      background-color: #2b6482; }
    .search-form-tabs > li > a > span {
      line-height: 29px;
      vertical-align: middle;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale; }
  .search-form-tabs > li.active > a, .search-form-tabs > li.active > a:hover, .search-form-tabs > li.active > a:focus {
    border: 1px solid #ffffff; }
  .search-form-tabs > li i {
    height: 25px;
    width: 25px;
    margin-right: 4px; }

#home-slider {
  min-width: 1000px;
  overflow-y: hidden;
  z-index: 1; }
  #home-slider .carousel-inner {
    background: #ffffff; }
    #home-slider .carousel-inner .item {
      background-repeat: no-repeat;
      box-sizing: content-box;
      height: 306px;
      font-size: 1.08em;
      line-height: 22px;
      padding: 79px 110px 0 504px;
      width: 450px; }
      #home-slider .carousel-inner .item > h3, #home-slider .carousel-inner .item p, #home-slider .carousel-inner .item button {
        margin: 16px 0; }
      #home-slider .carousel-inner .item > p {
        display: inline-block;
        width: 400px;
        margin-top: 1px; }
      #home-slider .carousel-inner .item h3 {
        font-size: 1.55em;
        color: #555555; }
      #home-slider .carousel-inner .item.text-right {
        padding: 79px 504px 0 11px; }
  #home-slider .carousel-control {
    background: #ffffff;
    filter: none;
    z-index: 1;
    -webkit-transition: width 0.2s ease-in;
    -o-transition: width 0.2s ease-in;
    transition: width 0.2s ease-in;
    opacity: 1;
    filter: alpha(opacity=100); }
    #home-slider .carousel-control .icon-prev,
    #home-slider .carousel-control .icon-next {
      width: 43px;
      height: 48px;
      margin-top: -20px;
      margin-left: -20px;
      background-color: #ffffff;
      background-position: 3px -1049px;
      opacity: 0.6;
      filter: alpha(opacity=60); }
      #home-slider .carousel-control .icon-prev:before,
      #home-slider .carousel-control .icon-next:before {
        content: ''; }
    #home-slider .carousel-control .icon-next {
      background-position: -27px -1049px; }
    #home-slider .carousel-control:hover .icon-prev,
    #home-slider .carousel-control:hover .icon-next {
      opacity: 1;
      filter: alpha(opacity=100); }
    @media screen and (max-width: 1200px) {
      #home-slider .carousel-control {
        width: 7%; } }

#online-actions-user .panel-group {
  margin-bottom: 0; }
  #online-actions-user .panel-group .panel {
    box-shadow: none; }
    #online-actions-user .panel-group .panel + .panel {
      margin-top: -1px; }
    #online-actions-user .panel-group .panel:first-child .panel-heading {
      border: 0; }
  #online-actions-user .panel-group .panel-heading {
    border-top: 1px solid #DBDBDB;
    border-radius: 0;
    height: 39px;
    font-size: 13px;
    cursor: pointer; }
  #online-actions-user .panel-group .panel-body {
    position: relative;
    padding: 0; }

#online-actions-user .dl-horizontal {
  font-size: 13px;
  margin-bottom: 0; }
  #online-actions-user .dl-horizontal dt {
    text-align: left;
    width: 50%;
    line-height: 20px; }
  #online-actions-user .dl-horizontal dd {
    text-align: right;
    margin-left: 50%; }
  #online-actions-user .dl-horizontal .hidden-fields {
    overflow: hidden; }

.form-control {
  box-shadow: none;
  text-align: left;
  -webkit-appearance: none; }
  .form-control:focus {
    box-shadow: none; }

.has-error .form-control {
  box-shadow: none; }
  .has-error .form-control:focus {
    box-shadow: none; }

.vertical-alignment-helper {
  display: table;
  height: 100%;
  width: 100%; }

.vertical-align-center {
  /* To center vertically */
  display: table-cell;
  vertical-align: middle; }

.modal-content {
  box-shadow: none;
  width: inherit;
  height: inherit;
  margin: 0 auto; }

.navbar-immobiliare {
  background-color: #2b6482;
  border: 0;
  margin-bottom: 0;
  border-radius: 0;
  min-height: 39px;
  z-index: 1030; }
  .navbar-immobiliare .navbar-text {
    color: #ffffff; }
  .navbar-immobiliare .navbar-nav {
    height: 39px; }
    .navbar-immobiliare .navbar-nav > li {
      margin: 0 0 0 4px; }
      .navbar-immobiliare .navbar-nav > li:first-child {
        margin-left: 0; }
    .navbar-immobiliare .navbar-nav > li.active > a,
    .navbar-immobiliare .navbar-nav > li > a,
    .navbar-immobiliare .navbar-nav > li > a:hover {
      margin-top: 5px;
      border-radius: 2px; }
    .navbar-immobiliare .navbar-nav > li > a {
      color: #ffffff;
      outline: none;
      -webkit-font-smoothing: antialiased; }
      .navbar-immobiliare .navbar-nav > li > a:hover, .navbar-immobiliare .navbar-nav > li > a:focus {
        color: #222222;
        background-color: #ffffff; }
    .navbar-immobiliare .navbar-nav > .active > a, .navbar-immobiliare .navbar-nav > .active > a:hover, .navbar-immobiliare .navbar-nav > .active > a:focus {
      color: #222222;
      background-color: #ffffff; }
  .navbar-immobiliare .navbar-main {
    position: relative;
    padding: 0 2px 0 0; }
    .navbar-immobiliare .navbar-main:before {
      content: '';
      position: absolute;
      height: 26px;
      width: 1px;
      right: -4px;
      top: 7px;
      background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIwLjE1Ii8+CiAgICA8c3RvcCBvZmZzZXQ9IjExJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIwLjU1Ii8+CiAgICA8c3RvcCBvZmZzZXQ9IjI2JSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIwLjk1Ii8+CiAgICA8c3RvcCBvZmZzZXQ9Ijc0JSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIwLjk1Ii8+CiAgICA8c3RvcCBvZmZzZXQ9Ijg5JSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIwLjU1Ii8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNmZmZmZmYiIHN0b3Atb3BhY2l0eT0iMC4xNSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
      background: -moz-linear-gradient(top, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.55) 11%, rgba(255, 255, 255, 0.95) 26%, rgba(255, 255, 255, 0.95) 74%, rgba(255, 255, 255, 0.55) 89%, rgba(255, 255, 255, 0.15) 100%);
      background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(255, 255, 255, 0.15)), color-stop(11%, rgba(255, 255, 255, 0.55)), color-stop(26%, rgba(255, 255, 255, 0.95)), color-stop(74%, rgba(255, 255, 255, 0.95)), color-stop(89%, rgba(255, 255, 255, 0.55)), color-stop(100%, rgba(255, 255, 255, 0.15)));
      background: -webkit-linear-gradient(top, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.55) 11%, rgba(255, 255, 255, 0.95) 26%, rgba(255, 255, 255, 0.95) 74%, rgba(255, 255, 255, 0.55) 89%, rgba(255, 255, 255, 0.15) 100%);
      background: -o-linear-gradient(top, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.55) 11%, rgba(255, 255, 255, 0.95) 26%, rgba(255, 255, 255, 0.95) 74%, rgba(255, 255, 255, 0.55) 89%, rgba(255, 255, 255, 0.15) 100%);
      background: -ms-linear-gradient(top, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.55) 11%, rgba(255, 255, 255, 0.95) 26%, rgba(255, 255, 255, 0.95) 74%, rgba(255, 255, 255, 0.55) 89%, rgba(255, 255, 255, 0.15) 100%);
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.55) 11%, rgba(255, 255, 255, 0.95) 26%, rgba(255, 255, 255, 0.95) 74%, rgba(255, 255, 255, 0.55) 89%, rgba(255, 255, 255, 0.15) 100%); }
    .navbar-immobiliare .navbar-main > li > a {
      font-weight: bold;
      padding: 5px 12px 5px 12px; }
  .navbar-immobiliare.navbar-fixed-top {
    min-width: 980px; }
    .navbar-immobiliare.navbar-fixed-top #user-profile {
      border-right: 4px solid rgba(158, 158, 158, 0.22);
      margin-right: -4px;
      width: 304px;
      border-radius: 0 0 3px 3px; }
  .navbar-immobiliare .navbar-right > li > a {
    padding: 5px 8px 4px; }
    .navbar-immobiliare .navbar-right > li > a:hover, .navbar-immobiliare .navbar-right > li > a:focus {
      color: #777777; }
  .navbar-immobiliare .navbar-nav > .open > a, .navbar-immobiliare .navbar-nav > .open > a:hover, .navbar-immobiliare .navbar-nav > .open > a:focus {
    padding-bottom: 8px;
    border-radius: 2px 2px 0 0;
    background-color: #ffffff;
    color: #222222;
    position: relative;
    z-index: 1001; }
  .navbar-immobiliare .navbar-nav > .dropdown > a:hover .caret {
    border-top-color: #222222;
    border-bottom-color: #222222; }
  .navbar-immobiliare .navbar-nav > .dropdown > a .caret {
    border-top-color: #ffffff;
    border-bottom-color: #ffffff; }
  .navbar-immobiliare .navbar-nav > .open > a .caret, .navbar-immobiliare .navbar-nav > .open > a:hover .caret, .navbar-immobiliare .navbar-nav > .open > a:focus .caret {
    border-top-color: #222222;
    border-bottom-color: #222222; }
  .navbar-immobiliare .navbar-link {
    color: #ffffff; }
    .navbar-immobiliare .navbar-link:hover {
      color: #222222; }

html,
body {
  height: 100%; }

.raleway {
  font-family: Raleway, sans-serif;
  font-weight: 200;
  font-weight: 300\0;
  letter-spacing: -0.01em\0; }

h1.raleway {
  font-size: 2.3em;
  font-weight: 300;
  margin-bottom: 1.2em; }

.container-fluid {
  height: 100%;
  padding-top: 1.5em;
  background-color: #3A78A3;
  position: relative; }

.device-img-bg {
  background-image: url("../images/splash_1x/sfondo-orizzontale.png");
  background-repeat: no-repeat;
  background-position: center 60px; }
  @media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2 / 1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {
    .device-img-bg {
      background-image: url("../images/splash_2x/sfondo-orizzontale.png");
      background-size: 1542px 223px; } }

.device-img {
  background-image: url("../images/splash_1x/device-orizzontale.png");
  background-position: center 0;
  background-repeat: no-repeat;
  height: 496px; }
  @media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2 / 1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {
    .device-img {
      background-image: url("../images/splash_2x/device-orizzontale.png");
      background-size: 496px 325px; } }

.splash-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #33393c;
  color: #ffffff;
  height: 155px;
  font-size: 19px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.splash-btns {
  padding: 20px 18px 12px; }
  .splash-btns > div {
    padding: 6px; }

#goto-site {
  color: #ffffff; }

@media screen and (orientation: portrait) {
  .container-fluid {
    padding-top: 3.2em; }
  h1 {
    margin-bottom: 1.1em; }
    h1 span {
      display: block; }
  .device-img-bg {
    background-image: url("../images/splash_1x/sfondo-verticale.png");
    background-position: center 92px; } }
  @media only screen and (orientation: portrait) and (-webkit-min-device-pixel-ratio: 2), only screen and (orientation: portrait) and (min--moz-device-pixel-ratio: 2), only screen and (orientation: portrait) and (-o-min-device-pixel-ratio: 2 / 1), only screen and (orientation: portrait) and (min-device-pixel-ratio: 2), only screen and (orientation: portrait) and (min-resolution: 192dpi), only screen and (orientation: portrait) and (min-resolution: 2dppx) {
    .device-img-bg {
      background-image: url("../images/splash_2x/sfondo-verticale.png");
      background-size: 704px 268px; } }

@media screen and (orientation: portrait) {
  .device-img {
    background-image: url("../images/splash_1x/device-verticale.png");
    height: 410px; } }
  @media only screen and (orientation: portrait) and (-webkit-min-device-pixel-ratio: 2), only screen and (orientation: portrait) and (min--moz-device-pixel-ratio: 2), only screen and (orientation: portrait) and (-o-min-device-pixel-ratio: 2 / 1), only screen and (orientation: portrait) and (min-device-pixel-ratio: 2), only screen and (orientation: portrait) and (min-resolution: 192dpi), only screen and (orientation: portrait) and (min-resolution: 2dppx) {
    .device-img {
      background-image: url("../images/splash_2x/device-verticale.png");
      background-size: 625px 410px; } }

@media screen and (orientation: portrait) {
  .splash-btns {
    padding: 31px;
    padding-bottom: 16px; }
  #goto-app {
    margin-top: 10px; }
  .splash-footer {
    height: 244px; } }