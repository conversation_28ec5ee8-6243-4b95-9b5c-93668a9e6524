'use strict';

var globalVars = !!window.globalVars ? window.globalVars : {};

var SplashView = function (iConfig, iDevice) {

    var root,
        device = (iDevice || 'ipad').toLowerCase() || '',
        deviceName = device[0].toUpperCase() + device.slice(1),
        config = iConfig[device] || {},
        timeCookie = config['cookieTime'] || 30,
        cookieName = 'splash' + deviceName,
        eventName = 'Splash' + deviceName,
        deepLinkEventName = 'DeepLink' + deviceName,
        downloadedCookieName = 'downloadedApp' + deviceName;

    /**
     * Init
     * @returns {root}
     */
    var initialize = function () {
        initEvents();
    };

    var iOSversion = function () {
        if (/iP(hone|od|ad)/.test(navigator.platform)) {
            // supports iOS 2.0 and later: <http://bit.ly/TJjs1V>
            var v = (navigator.appVersion).match(/OS (\d+)_(\d+)_?(\d+)?/);
            return parseFloat(parseInt(v[1], 10) + "." + parseInt(v[2], 10) + "." + parseInt(v[3] || 0, 10));
        }
    }

    var setCookie = function(name, value, expires, path, domain, secure){
        //TODO setCookie disabled, don't commit this status, delete next line before
        var today = new Date();
        today.setTime( today.getTime() );

        expires = expires * 1000 * 60 * 60 * 24;

        var expires_date = new Date( today.getTime() + (expires) );

        document.cookie = name + "=" +escape( value ) +
            ( ( expires ) ? ";expires=" + expires_date.toGMTString() : "" ) +
            ( ( path ) ? ";path=" + path : "" ) +
            ( ( domain ) ? ";domain=" + domain : "" ) +
            ( ( secure ) ? ";secure" : "" );
    }

    var getCookie = function (name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    var downloadApp = function () {

        setCookie(downloadedCookieName, 1, 365, '/');

        timeCookie = 90;
        execRedirect(config['downloadAppUrl'] || '', 25);

    };

    var goToSite = function () {

        var redirectUrl = document.referrer ? document.referrer : config['siteUrl'] || '';

        if (!getCookie(cookieName)) {
            setCookie(cookieName, 1, timeCookie, '/');
        }

        execRedirect(redirectUrl);

    };

    var goToApp = function (ev) {		
		
        /* Note: we need the href attribute on the element to work properly */
        var event = ev || window.event,
            el = event.target || event.srcElement;		


        if (iOSversion() >= 9) {
           return true;
        }		
		
        downloadApp();		

        return true;

    };

    /**
     * Init events
     * @return {root}
     */
    var initEvents = function () {

        $(config['downloadAppLink']).on('click', downloadApp);
        $(config['openAppLink']).on('click', goToApp);
        $(config['goSiteLink']).on('click', goToSite);

        return root;

    };

    var execRedirect = function (redirect, timeout) {

        var sep,
            sepPos;

        if (!!config['trackingPars']) {
            sep = redirect.search(/\?/) >= 0 ? '&' : '?';
            redirect += sep + config['trackingPars'].trim('?').trim('&');
        }

        setTimeout(function () {
            location.href = redirect;
        }, timeout || 0);

        return root;

    }

    root = {
        initialize: initialize
    };

    return root;

};

var config = {
    ipad: {
        openAppLink: '#open-app',
        downloadAppLink: '#download-app',
        goSiteLink: '#goto-site',
        urlAppSchema: 'spitogrp2://etc',
        downloadAppUrl: 'https://itunes.apple.com/gr/app/spitogatos.gr-700.000+-angelies/id1057971912?mt=8&l=el',
        trackingPars: 'internalSource=splashIpad',
        siteUrl: goToSiteUrl,
        cookieTime: 30
    }
};

var splashView = new SplashView(config).initialize();