{% extends "_layouts/site-rodi" %}

{% set title = entry.title %}

{# Get the entry with the selected ID #}
{% set termsPage = craft.entries.id(19903).one() %}

{% block pagestyles %}
{% endblock %}

{% block body %}

<style>
    .main-wrapper {
        padding-top: 200px;
        padding-bottom: 100px;
        overflow: hidden;
    }

    video {
        /* override other styles to make responsive */
        width: 100% !important;
        height: auto !important;
        max-width: 350px;
    }
</style>

<div class="main-wrapper">

    <div class="container">
        <div class="col6 col-md-12">

            <style>
                .new-btn {
                    padding: 16px;
                    font-size: 16px;
                    color: #fff;
                    background: #6E9537;
                    display: block;
                    max-width: 340px;
                    margin: 0 auto;
                    box-shadow: none;
                    border: 0;
                    border-radius: 8px;
                    width: 100%;
                    cursor: pointer;
                }
                .new-btn2 {
                    padding: 16px;
                    font-size: 18px;
                    color: #fff;
                    border:2px solid #7A5B42;
                    background: #fff;
                    display: block;
                    max-width: 340px;
                    margin: 0 auto;
                    box-shadow: none;
                    border-radius: 8px;
                    width: 100%;
                    color: #7A5B42;
                    cursor: pointer;
                }
                .new-btn2.clicked.wrong {
                    border:2px solid red;
                    color: red;
                }
                .new-btn2.clicked.correct {
                    background: #6E9537;
                    border:2px solid #6E9537;
                    color: #fff;
                }
            </style>

            {% set section = craft.app.sections.getSectionByHandle('contestRecord') %}
            {% if section %}
                {% set entryType = section.entryTypes[0] %}
                {% set entry = create({
                    class: 'craft\\elements\\Entry',
                    sectionId: section.id,
                    typeId: entryType.id,
                    siteId: craft.app.sites.currentSite.id,
                }) %}
            {% else %}
                {% set entry = null %}
            {% endif %}

            {% if entry %}

            {# Display any flash messages or errors #}
            {% if craft.app.session.hasFlash('notice') %}
                <div class="success-message">{{ craft.app.session.getFlash('notice') }}</div>
            {% endif %}

            {% if craft.app.session.hasFlash('error') %}
                <div class="error-message">{{ craft.app.session.getFlash('error') }}</div>
            {% endif %}

            <form class="form form--test2 " method="post" enctype="multipart/form-data" accept-charset="UTF-8">
                {{ csrfInput() }}
                {{ actionInput('entries/save-entry') }}
                {{ redirectInput('/thank-you') }}

                {{ hiddenInput('sectionId', section.id) }}
                {{ hiddenInput('typeId', entryType.id) }}
                {{ hiddenInput('siteId', craft.app.sites.currentSite.id) }}
                {{ hiddenInput('enabled', true) }}
                {# For structure sections, add these parameters #}
                {% if section.type == 'structure' %}
                    {{ hiddenInput('parentId', '') }}
                    {{ hiddenInput('level', 1) }}
                {% endif %}

                <label for="title">Title *</label>
                <input type="text" id="title" name="title" value="{{ entry.title }}" required>
                {{ entry.getErrors('title') ? '<ul class="errors">' ~ entry.getErrors('title')|join('<li>') ~ '</ul>' }}

                <label for="contestConsent">Consent *</label>
                <input type="checkbox" id="contestConsent" name="fields[contestConsent]" value="1" required {{ entry.contestConsent ? 'checked' : '' }}>
                {{ entry.getErrors('contestConsent') ? '<ul class="errors">' ~ entry.getErrors('contestConsent')|join('<li>') ~ '</ul>' }}

                <label for="contestEmail">Email *</label>
                <input type="email" id="contestEmail" name="fields[contestEmail]" value="{{ entry.contestEmail }}" required>
                {{ entry.getErrors('contestEmail') ? '<ul class="errors">' ~ entry.getErrors('contestEmail')|join('<li>') ~ '</ul>' }}

                <label for="contestFiles">Upload File *</label>
                <input type="file" id="contestFiles" name="fields[contestFiles]" accept="image/*,.pdf" required>
                {{ entry.getErrors('contestFiles') ? '<ul class="errors">' ~ entry.getErrors('contestFiles')|join('<li>') ~ '</ul>' }}

                <button type="submit" class="button new-btn">Submit</button>
            </form>
            {% else %}
                <div class="error-message">
                    <p>Contest form is currently unavailable. The contestRecord section needs to be created in Craft CMS admin.</p>
                    {# Debug info #}
                    {% if craft.app.config.general.devMode %}
                        <p>Debug: Section handle 'contestRecord' not found.</p>
                    {% endif %}
                </div>
            {% endif %}

        </div>
        <div class="col6 col-md-12">
            <div style="display: flex; width: 100%; justify-content: center;">
                <video  autoplay muted controls loop>
                    <source src="{{ alias('@assetBaseUrl') }}/videos/Diagonismos_Lemonada_Vysinada.mp4"
                            type="video/mp4">
                    Your browser does not support web videos.
                </video>
            </div>
        </div>
    </div>

</div><!--end of main-wrapper-->


{% endblock %}

{% block pagescripts %}
<script src="https://use.fontawesome.com/16c36c55d0.js"></script>
{% endblock %}