{% extends "_layouts/site-rodi" %}

{% set title = entry.title %}

{# Get the entry with the selected ID #}
{% set termsPage = craft.entries.id(19903).one() %}

{% block pagestyles %}
{% endblock %}

{% block body %}

<style>
    .main-wrapper {
        padding-top: 200px;
        padding-bottom: 100px;
        overflow: hidden;
    }

    video {
        /* override other styles to make responsive */
        width: 100% !important;
        height: auto !important;
        max-width: 350px;
    }
</style>

<div class="main-wrapper">

    <div class="container">
        <div class="col6 col-md-12">

            <style>
                .new-btn {
                    padding: 16px;
                    font-size: 16px;
                    color: #fff;
                    background: #6E9537;
                    display: block;
                    max-width: 340px;
                    margin: 0 auto;
                    box-shadow: none;
                    border: 0;
                    border-radius: 8px;
                    width: 100%;
                    cursor: pointer;
                }
                .new-btn2 {
                    padding: 16px;
                    font-size: 18px;
                    color: #fff;
                    border:2px solid #7A5B42;
                    background: #fff;
                    display: block;
                    max-width: 340px;
                    margin: 0 auto;
                    box-shadow: none;
                    border-radius: 8px;
                    width: 100%;
                    color: #7A5B42;
                    cursor: pointer;
                }
                .new-btn2.clicked.wrong {
                    border:2px solid red;
                    color: red;
                }
                .new-btn2.clicked.correct {
                    background: #6E9537;
                    border:2px solid #6E9537;
                    color: #fff;
                }
            </style>

            {% set section = craft.app.sections.getSectionByHandle('contestRecord') %}
            {% set entryType = section.entryTypes %}
            {% set entry = create({
                class: 'craft\\elements\\Entry',
                sectionId: section.id
            }) %}

            <form class="form form--test2 " method="post" enctype="multipart/form-data" accept-charset="UTF-8">
                {{ csrfInput() }}
                {{ actionInput('entries/save-entry') }}
                {{ redirectInput('/thank-you') }}

                {{ hiddenInput('sectionId', section.id) }}
                {{ hiddenInput('enabled', true) }}

                <label for="title">Title *</label>
                <input type="text" id="title" name="title" value="{{ entry.title }}" required>
                {{ entry.getErrors('title') ? '<ul class="errors">' ~ entry.getErrors('title')|join('<li>') ~ '</ul>' }}

                <label for="contestConsent">Consent *</label>
                <input type="checkbox" id="contestConsent" name="fields[contestConsent]" value="1" required {{ entry.contestConsent ? 'checked' : '' }}>
                {{ entry.getErrors('contestConsent') ? '<ul class="errors">' ~ entry.getErrors('contestConsent')|join('<li>') ~ '</ul>' }}

                <label for="contestEmail">Email *</label>
                <input type="email" id="contestEmail" name="fields[contestEmail]" value="{{ entry.contestEmail }}" required>
                {{ entry.getErrors('contestEmail') ? '<ul class="errors">' ~ entry.getErrors('contestEmail')|join('<li>') ~ '</ul>' }}

                <label for="contestFiles">Upload File *</label>
                <input type="file" id="contestFiles" name="fields[contestFiles][]" accept="image/*,.pdf" required>
                {{ entry.getErrors('contestFiles') ? '<ul class="errors">' ~ entry.getErrors('contestFiles')|join('<li>') ~ '</ul>' }}

                <button type="submit" class="button new-btn">Submit</button>
            </form>


            <div class="contestStep2">

                <div id="mc_embed_shell">
              {#  <link href="//cdn-images.mailchimp.com/embedcode/classic-061523.css" rel="stylesheet" type="text/css">#}
                <style type="text/css">
                    #mc_embed_shell {
                        padding: 25px;
                    }

                    #mce-responses .response {
                        font-weight: bold;
                        margin-bottom: 20px;
                        font-size: 22px;
                        text-align: center;
                    }

                    #mc-embedded-subscribe-form input[type=checkbox] {

                    }

                    #mergeRow-gdpr {
                    }

                    #mergeRow-gdpr fieldset label {

                    }

                    #mc-embedded-subscribe-form .mc_fieldset {
                        border: none;
                        min-height: 0px;
                        padding-bottom: 0px;
                    }


                    .form--test2 label {
                        display: block;
                        font-size: 19px;
                        margin-bottom: 5px;
                    }
                    .termsLink {
                        color: #6E9537;
                    }
                    .termsLink:hover {
                        color: #7A5B42;
                    }
                </style>
                <div id="mc_embed_signup">
                    <form action="https://christodouloufamily.us10.list-manage.com/subscribe/post?u=fcb99f181afa4ffe99380792e&id=dffc81c33d&v_id=5353&f_id=00d6aee5f0"
                          method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form"
                          class="validate form form--test2 "  target="_blank">

                        <div id="mc_embed_signup_scroll">

                            <div class="form__group">
                                <h2>Λάβε μέρος στο διαγωνισμό μας για να κερδίσεις πλούσια δώρα!</h2>
                                <ul>
                                    <li>1 μεγάλος νικητής κερδίζει ένα ταξίδι στο Ναύπλιο για 2 άτομα </li>
                                    <li>3 νικητές θα κερδίσουν τους Χυμούς του Μήνα </li>
                                </ul>
                            </div>

                            <div class="mc-field-group form__group">
                                <input type="email" name="EMAIL" class="required email form__input" id="mce-EMAIL"
                                       required="" value="" placeholder="Email">
                            </div>
                            <div class="mc-field-group form__group">
                                <input type="text" name="FNAME" class="required text form__input" id="mce-FNAME" value="" placeholder="Όνομα" required="">
                            </div>
                            <div class="mc-field-group form__group">
                                <input type="text" name="LNAME" class="required text form__input" id="mce-LNAME" value="" placeholder="Επίθετο" required="">
                            </div>
                            <div id="mergeRow-gdpr" class="mergeRow gdpr-mergeRow content__gdprBlock mc-field-group">
                                <div class="content__gdpr">
                                    <div class="mc_fieldset gdprRequired mc-field-group"
                                              name="interestgroup_field">
                                        <div class="custom-checkbox">
                                            <input type="checkbox"
                                                   id="gdpr_61621"
                                                   name="gdpr[61621]"
                                                   class="gdpr"
                                                   value="Y"
                                                   checked
                                            >
                                            <label class="checkbox subfield" for="gdpr_61621">Ναι, αποδέχομαι την
                                                επικοινωνία μέσω newsletter</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form__group">
                                <div class="custom-checkbox">
                                    <input type="checkbox"
                                           id="acceptTheTerms"
                                           name="acceptTheTerms"
                                           class=""
                                           value="Y"
                                           checked
                                           required
                                    >
                                    <label class="checkbox subfield" for="acceptTheTerms">Αποδέχομαι τους <a class="termsLink"
                                            href="{{ termsPage.url }}" target="_blank">όρους του διαγωνισμού</a></label>
                                </div>
                            </div>
                          {#  get tag id from embed form editor#}
                            <div hidden=""><input type="hidden" name="tags" value="14518007"></div>
                            <div id="mce-responses" class="clear">
                                <div class="response" id="mce-error-response" style="display: none;"></div>
                                <div class="response" id="mce-success-response" style="display: none;"></div>
                            </div>
                            <div style="position: absolute; left: -5000px;" aria-hidden="true"><input type="text"
                                                                                                      name="b_fcb99f181afa4ffe99380792e_dffc81c33d"
                                                                                                      tabindex="-1"
                                                                                                      value=""></div>
                            <div class="clear"><input type="submit" name="subscribe" id="mc-embedded-subscribe"
                                                      class="button new-btn" value="Δήλωσε Συμμετοχή"></div>
                        </div>
                    </form>
                </div>
                <script type="text/javascript" src="//s3.amazonaws.com/downloads.mailchimp.com/js/mc-validate.js"></script><script type="text/javascript">(function($) {window.fnames = new Array(); window.ftypes = new Array();fnames[0]='EMAIL';ftypes[0]='email';fnames[1]='FNAME';ftypes[1]='text';fnames[2]='LNAME';ftypes[2]='text';fnames[3]='ADDRESS';ftypes[3]='address';fnames[4]='PHONE';ftypes[4]='phone';/*
 * Translated default messages for the $ validation plugin.
 * Locale: EL
 */
                $.extend($.validator.messages, {
                    required: "Αυτό το πεδίο είναι υποχρεωτικό.",
                    remote: "Παρακαλώ διορθώστε αυτό το πεδίο.",
                    email: "Παρακαλώ εισάγετε μια έγκυρη διεύθυνση email.",
                    url: "Παρακαλώ εισάγετε ένα έγκυρο URL.",
                    date: "Παρακαλώ εισάγετε μια έγκυρη ημερομηνία.",
                    dateISO: "Παρακαλώ εισάγετε μια έγκυρη ημερομηνία (ISO).",
                    number: "Παρακαλώ εισάγετε έναν έγκυρο αριθμό.",
                    digits: "Παρακαλώ εισάγετε μόνο αριθμητικά ψηφία.",
                    creditcard: "Παρακαλώ εισάγετε έναν έγκυρο αριθμό πιστωτικής κάρτας.",
                    equalTo: "Παρακαλώ εισάγετε την ίδια τιμή ξανά.",
                    accept: "Παρακαλώ εισάγετε μια τιμή με έγκυρη επέκταση αρχείου.",
                    maxlength: $.validator.format("Παρακαλώ εισάγετε μέχρι και {0} χαρακτήρες."),
                    minlength: $.validator.format("Παρακαλώ εισάγετε τουλάχιστον {0} χαρακτήρες."),
                    rangelength: $.validator.format("Παρακαλώ εισάγετε μια τιμή με μήκος μεταξύ {0} και {1} χαρακτήρων."),
                    range: $.validator.format("Παρακαλώ εισάγετε μια τιμή μεταξύ {0} και {1}."),
                    max: $.validator.format("Παρακαλώ εισάγετε μια τιμή μικρότερη ή ίση του {0}."),
                    min: $.validator.format("Παρακαλώ εισάγετε μια τιμή μεγαλύτερη ή ίση του {0}.")
                });}(jQuery));var $mcj = jQuery.noConflict(true);</script></script>
            </div>

            </div>

        </div>
        <div class="col6 col-md-12">
            <div style="display: flex; width: 100%; justify-content: center;">
                <video  autoplay muted controls loop>
                    <source src="{{ alias('@assetBaseUrl') }}/videos/Diagonismos_Lemonada_Vysinada.mp4"
                            type="video/mp4">
                    Your browser does not support web videos.
                </video>
            </div>
        </div>
    </div>

</div><!--end of main-wrapper-->


{% endblock %}

{% block pagescripts %}
<script src="https://use.fontawesome.com/16c36c55d0.js"></script>
{% endblock %}