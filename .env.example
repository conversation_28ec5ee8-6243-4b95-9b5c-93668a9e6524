# The environment Craft is currently running in (dev, staging, production, etc.)
ENVIRONMENT=dev

# The application ID used to to uniquely store session and cache data, mutex locks, and more
APP_ID=

# The secure key Craft will use for hashing and encrypting data
SECURITY_KEY=

# Database Configuration
DB_DRIVER=mysql
DB_SERVER=127.0.0.1
DB_PORT=3306
DB_DATABASE=
DB_USER=root
DB_PASSWORD=
DB_SCHEMA=public
DB_TABLE_PREFIX=

# The URI segment that tells Craft to load the control panel
CP_TRIGGER=admin
