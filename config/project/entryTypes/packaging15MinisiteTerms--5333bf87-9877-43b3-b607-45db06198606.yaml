fieldLayouts:
  78ae1510-aea2-4570-8200-71f71b56ca39:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: a13d2c9c-57bd-4ae7-b753-bca55e49ed45
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 5941779d-ef2e-4178-831c-219df02c3671 # Body
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a6a0ab55-e057-4a64-baf8-24098d095ba9
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 2a029421-8eba-4c61-9b11-bfc7580175b1
        userCondition: null
handle: packaging15MinisiteTerms
hasTitleField: false
name: 'Packaging 1.5 Minisite Terms'
section: 8e84cdd1-0423-4bbd-8c5e-b4a98e3bace8 # Packaging 1.5 Minisite Terms
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
sortOrder: 1
titleFormat: '{section.name|raw}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
