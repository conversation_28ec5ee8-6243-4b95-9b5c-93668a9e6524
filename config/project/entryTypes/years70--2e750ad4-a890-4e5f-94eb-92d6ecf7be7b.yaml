fieldLayouts:
  b9599a12-7f61-4eb9-9def-5f4a173dc921:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: df73bd11-8760-452b-b094-c50c3f0ede39
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 5941779d-ef2e-4178-831c-219df02c3671 # Body
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4eb0031e-0c67-4c40-82ed-ae5b9dee22cf
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: bce80e34-4d6a-464f-b54d-93202ab8b871 # Title 1
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f44e8094-c34d-4569-95a0-72eeb0559799
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: d931f4fa-d79c-419b-9b5b-51a9cb65421f # Text 1
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5cc70e47-a304-4dce-838d-d1320fe13f8b
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 0820ef6a-4fef-4699-8d68-312714ca5b33 # Title 2
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 685674ee-57d5-47d0-a111-1b62c616d2aa
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: bbe0287b-3262-4c77-a605-87c7f77dcdac # Text 2
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1d5ba3c1-eac0-4e7e-a6b4-c9d01782cb18
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: f7b8325e-d0c5-4d16-a7af-5296771612b7
        userCondition: null
handle: years70
hasTitleField: true
name: '70 Years'
section: abf8c872-d93e-4031-859f-fd45fd2def43 # 70 Years
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
sortOrder: 1
titleFormat: '{section.name|raw}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
