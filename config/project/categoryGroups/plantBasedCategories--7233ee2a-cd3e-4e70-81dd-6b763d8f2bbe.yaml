defaultPlacement: end
fieldLayouts:
  6e3f2092-c432-4850-bbb6-0316b94025fc:
    tabs:
      -
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            id: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\TitleField
            warning: null
            width: 100
          -
            fieldUid: 5941779d-ef2e-4178-831c-219df02c3671 # Body
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
        name: General
        sortOrder: 1
handle: plantBasedCategories
name: 'Plant Based Categories'
siteSettings:
  15d8ecf2-def0-4742-abe3-9ca5af0a68e7: # Chris Family Juices
    hasUrls: true
    template: /plant-based-products
    uriFormat: '{% if object.level == 1 %}plant-based-products/categories/{slug}{% else %}{parent.uri}/{slug}{% endif %}'
  c6955485-107d-4607-ae9c-8fbf715baf87: # Χυμοί Οικογένεια Χριστοδούλου
    hasUrls: true
    template: /plant-based-products
    uriFormat: '{% if object.level == 1 %}plant-based-products/categories/{slug}{% else %}{parent.uri}/{slug}{% endif %}'
  e5ee83fe-46a2-4636-9c1a-575cfce322b8: # Christodoulou Family (de)
    hasUrls: true
    template: /plant-based-products
    uriFormat: '{% if object.level == 1 %}plant-based-products/categories/{slug}{% else %}{parent.uri}/{slug}{% endif %}'
structure:
  maxLevels: null
  uid: 3a29c408-22f1-4c37-afad-39706f7a795c
