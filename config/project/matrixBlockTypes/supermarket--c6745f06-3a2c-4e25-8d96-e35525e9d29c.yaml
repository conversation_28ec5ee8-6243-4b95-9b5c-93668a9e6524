field: 69cbead0-04a3-443a-8280-f0601302e71c # Supermarket Availability
fieldLayouts:
  0390c8be-35c7-4b49-a1f8-33639d293484:
    tabs:
      -
        elements:
          -
            fieldUid: fdd5b34e-fe4b-48d7-89a1-e5670021a594 # Supermarket Brand
            instructions: null
            label: null
            required: 1
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: dd4a756c-7430-4157-b8eb-be3ee4dd748d # Is Available?
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: c3b8533a-d437-4816-be52-6d48fddcb2fa # Eshop LInk
            instructions: null
            label: null
            required: 1
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
        name: Content
        sortOrder: 1
fields:
  c3b8533a-d437-4816-be52-6d48fddcb2fa: # Eshop LInk
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: eshopLink
    instructions: ''
    name: 'Eshop LInk'
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: text
      initialRows: '4'
      multiline: ''
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  dd4a756c-7430-4157-b8eb-be3ee4dd748d: # Is Available?
    columnSuffix: null
    contentColumnType: boolean
    fieldGroup: null
    handle: isAvailable
    instructions: ''
    name: 'Is Available?'
    searchable: true
    settings:
      default: true
      offLabel: null
      onLabel: null
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Lightswitch
  fdd5b34e-fe4b-48d7-89a1-e5670021a594: # Supermarket Brand
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: supermarketBrand
    instructions: ''
    name: 'Supermarket Brand'
    searchable: true
    settings:
      allowSelfRelations: false
      limit: '1'
      localizeRelations: false
      selectionLabel: ''
      showSiteMenu: true
      source: null
      sources:
        - 'section:a8e05caa-e5cd-43a9-b8b0-27045a6f602f' # Sellers
      targetSiteId: null
      validateRelatedElements: false
      viewMode: null
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Entries
handle: supermarket
name: Supermarket
sortOrder: 1
