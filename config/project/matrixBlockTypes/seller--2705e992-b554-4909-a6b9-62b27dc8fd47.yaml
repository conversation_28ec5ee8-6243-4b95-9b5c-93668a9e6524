field: 2386acbc-fd32-4f38-83bf-32315731e8bc # Sellers
fieldLayouts:
  b691447d-cb93-416a-9474-3490950ce597:
    tabs:
      -
        elements:
          -
            fieldUid: af96d551-4aec-41f1-b14a-9458e6cb0b7e # Seller 
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: b25140a0-fed1-4b2c-b59c-bd0f1352882f # Links
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
        name: Content
        sortOrder: 1
fields:
  af96d551-4aec-41f1-b14a-9458e6cb0b7e: # Seller 
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: seller
    instructions: ''
    name: 'Seller '
    searchable: true
    settings:
      allowSelfRelations: false
      limit: '1'
      localizeRelations: false
      selectionLabel: 'Add a seller'
      showSiteMenu: true
      source: null
      sources:
        - 'section:a8e05caa-e5cd-43a9-b8b0-27045a6f602f' # Sellers
      targetSiteId: null
      validateRelatedElements: false
      viewMode: null
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Entries
  b25140a0-fed1-4b2c-b59c-bd0f1352882f: # Links
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: links
    instructions: ''
    name: Links
    searchable: true
    settings:
      columns:
        new1:
          width: ''
        new2:
          width: ''
      contentTable: '{{%stc_7_links}}'
      fieldLayout: table
      maxRows: null
      minRows: null
      propagationMethod: all
      selectionLabel: 'Add a row'
      staticField: null
    translationKeyFormat: null
    translationMethod: site
    type: verbb\supertable\fields\SuperTableField
handle: seller
name: Seller
sortOrder: 1
