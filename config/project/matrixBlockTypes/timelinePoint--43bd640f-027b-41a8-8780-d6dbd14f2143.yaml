field: d86b99b8-7ca7-4f34-976a-d805bf20e22c # Timeline
fieldLayouts:
  eadd28f6-8a5a-43d9-be04-c0487b8b3ca9:
    tabs:
      -
        elements:
          -
            fieldUid: 63cf7042-0687-4e99-9efc-488974db483f # Year
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: ac09b7aa-f395-4ba5-9066-07d0a705d6d5 # Image
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: 312ff1b3-e450-4bfa-80c6-b1558877754e # Title
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: e5533554-ad02-4e13-a3ab-8b66f5a675d4 # Text
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
        name: Content
        sortOrder: 1
fields:
  63cf7042-0687-4e99-9efc-488974db483f: # Year
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: timelineYear
    instructions: ''
    name: Year
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: text
      initialRows: '4'
      multiline: ''
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  312ff1b3-e450-4bfa-80c6-b1558877754e: # Title
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: timelineTitle
    instructions: ''
    name: Title
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: text
      initialRows: '4'
      multiline: ''
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\PlainText
  ac09b7aa-f395-4ba5-9066-07d0a705d6d5: # Image
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: timelineImage
    instructions: ''
    name: Image
    searchable: true
    settings:
      allowSelfRelations: false
      allowUploads: true
      allowedKinds: null
      defaultUploadLocationSource: 'volume:c5209cb0-5f8a-44be-b657-67c87d21828b' # Hero Banners
      defaultUploadLocationSubpath: ''
      limit: '1'
      localizeRelations: false
      previewMode: full
      restrictFiles: ''
      selectionLabel: ''
      showSiteMenu: true
      showUnpermittedFiles: false
      showUnpermittedVolumes: true
      singleUploadLocationSource: 'volume:99fc83f4-ad02-4baf-b609-a8b3249d1a3e' # Our Family
      singleUploadLocationSubpath: ''
      source: null
      sources: '*'
      targetSiteId: null
      useSingleFolder: true
      validateRelatedElements: false
      viewMode: large
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Assets
  e5533554-ad02-4e13-a3ab-8b66f5a675d4: # Text
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: timelineText
    instructions: ''
    name: Text
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: text
      initialRows: '4'
      multiline: '1'
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\PlainText
handle: timelinePoint
name: 'Timeline Point'
sortOrder: 1
