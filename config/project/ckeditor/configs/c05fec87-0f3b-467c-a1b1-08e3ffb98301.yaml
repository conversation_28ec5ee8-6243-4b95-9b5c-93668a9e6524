headingLevels:
  - 1
  - 2
  - 3
  - 4
  - 5
  - 6
name: <PERSON>
options:
  alignment:
    options:
      - left
      - right
  fontSize:
    options:
      - tiny
      - small
      - big
      - huge
  style:
    definitions:
      -
        classes:
          - red-heading
        element: h2
        name: 'Red heading'
      -
        classes:
          - vibrant-code
        element: pre
        name: 'Vibrant code'
      -
        classes:
          - marker
        element: span
        name: Marker
toolbar:
  - sourceEditing
  - heading
  - style
  - alignment
  - '|'
  - bold
  - italic
  - link
  - insertImage
  - fontColor
  - fontBackgroundColor
  - fontSize
  - bulletedList
