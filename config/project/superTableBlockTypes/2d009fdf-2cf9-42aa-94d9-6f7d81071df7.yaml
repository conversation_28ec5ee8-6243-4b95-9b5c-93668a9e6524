field: 0a4966a3-0006-4b91-ba72-1bff0e319456 # Questions
fieldLayouts:
  423adcd6-461d-4c6f-a26e-7bec04ec79d8:
    tabs:
      -
        elements:
          -
            fieldUid: 357ecd52-12a7-4c56-b1fe-6dbc4acfe0aa # Title
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: c1cd685e-a9cd-4629-8d14-4201c29c52d0 # Weight
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
          -
            fieldUid: 8128ce88-f5e4-4e2c-9a59-a87a8656a3aa # Answers
            instructions: null
            label: null
            required: 0
            tip: null
            type: craft\fieldlayoutelements\CustomField
            warning: null
            width: 100
        name: Content
        sortOrder: 1
fields:
  357ecd52-12a7-4c56-b1fe-6dbc4acfe0aa: # Title
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: testQuestionTitle
    instructions: ''
    name: Title
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: text
      initialRows: '4'
      multiline: ''
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\PlainText
  8128ce88-f5e4-4e2c-9a59-a87a8656a3aa: # Answers
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: testAnswers
    instructions: ''
    name: Answers
    searchable: true
    settings:
      contentTable: '{{%matrixcontent_testanswers}}'
      maxBlocks: null
      minBlocks: null
      propagationKeyFormat: null
      propagationMethod: all
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Matrix
  c1cd685e-a9cd-4629-8d14-4201c29c52d0: # Weight
    columnSuffix: null
    contentColumnType: integer(10)
    fieldGroup: null
    handle: testQuestionWeight
    instructions: ''
    name: Weight
    searchable: true
    settings:
      decimals: 0
      defaultValue: null
      max: null
      min: 1
      prefix: null
      previewCurrency: null
      previewFormat: decimal
      size: null
      suffix: null
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Number
