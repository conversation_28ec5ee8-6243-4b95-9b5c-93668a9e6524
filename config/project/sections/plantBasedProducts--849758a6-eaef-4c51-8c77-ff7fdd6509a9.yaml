defaultPlacement: end
enableVersioning: true
handle: plantBasedProducts
name: 'Plant Based Products'
previewTargets:
  -
    __assoc__:
      -
        - label
        - 'Primary entry page'
      -
        - urlFormat
        - '{url}'
propagationMethod: all
siteSettings:
  15d8ecf2-def0-4742-abe3-9ca5af0a68e7: # Chris Family Juices
    enabledByDefault: true
    hasUrls: true
    template: plant-based-products/_entry
    uriFormat: '{% if object.level == 1 %}plant-based-products/{slug}{% else %}{parent.uri}/{slug}{% endif %}'
  c6955485-107d-4607-ae9c-8fbf715baf87: # Χυμοί Οικογένεια Χριστοδούλου
    enabledByDefault: true
    hasUrls: true
    template: plant-based-products/_entry
    uriFormat: '{% if object.level == 1 %}plant-based-products/{slug}{% else %}{parent.uri}/{slug}{% endif %}'
  e5ee83fe-46a2-4636-9c1a-575cfce322b8: # <PERSON><PERSON><PERSON><PERSON> Family (de)
    enabledByDefault: true
    hasUrls: true
    template: plant-based-products/_entry
    uriFormat: '{% if object.level == 1 %}plant-based-products/{slug}{% else %}{parent.uri}/{slug}{% endif %}'
structure:
  maxLevels: null
  uid: 902c0ff8-f3e3-4ffc-ab6e-0610e7d6bcae
type: structure
